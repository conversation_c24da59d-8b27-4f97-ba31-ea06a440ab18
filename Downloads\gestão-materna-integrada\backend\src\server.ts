import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server } from 'socket.io';
import dotenv from 'dotenv';
import { WhatsAppClient } from './services/whatsapp';
import { GeminiAIService } from './services/gemini';
import { whatsappAutoStart } from './services/whatsappAutoStart';
// import ProactiveSchedulerService from './services/proactiveScheduler'; // Desabilitado temporariamente
import { setupRoutes } from './routes';
// import { setupWebhooks } from './webhooks'; // Desabilitado temporariamente
// MongoDB removido - usando Supabase
// import { connectDatabase } from './config/database';
import { eventCaptureMiddleware, errorEventMiddleware, emitSystemStartup, emitSystemShutdown } from './middleware/eventCapture';

dotenv.config();

const app = express();
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: [
      'http://localhost:5173',
      'http://localhost:5174',
      'http://localhost:5175',
      'http://localhost:3000',
      process.env.FRONTEND_URL || 'http://localhost:5173'
    ],
    methods: ['GET', 'POST']
  }
});

// Middlewares de segurança
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false
}));

// Rate limiting global
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // máximo 100 requests por IP
  message: {
    error: 'Muitas requisições deste IP, tente novamente mais tarde.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use(limiter);

// CORS configurado para múltiplas portas
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:3000',
    process.env.CORS_ORIGIN || 'http://localhost:5173'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

// Body parsing
app.use(express.json({
  limit: process.env.MAX_FILE_SIZE || '10mb',
  verify: (req, res, buf) => {
    // Verificação adicional de segurança para JSON
    try {
      JSON.parse(buf.toString());
    } catch (e) {
      throw new Error('JSON inválido');
    }
  }
}));

app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Middleware de captura de eventos
app.use(eventCaptureMiddleware);

// Inicialização dos serviços
const geminiService = new GeminiAIService();
const whatsappClient = new WhatsAppClient(io, geminiService);
// const proactiveScheduler = new ProactiveSchedulerService(whatsappClient, geminiService); // Desabilitado temporariamente

// Inicializar WhatsApp automático (wppConnect integração)
console.log('🚀 Iniciando WhatsApp automático...');
whatsappAutoStart.start().then(success => {
  if (success) {
    console.log('✅ WhatsApp automático iniciado com sucesso!');
  } else {
    console.warn('⚠️ WhatsApp automático falhou, usando método tradicional');
  }
}).catch(error => {
  console.error('❌ Erro no WhatsApp automático:', error);
  console.log('🔄 Continuando com método tradicional...');
});

// Configuração das rotas e webhooks
setupRoutes(app, whatsappClient, geminiService);
// setupWebhooks(app, whatsappClient, geminiService); // Desabilitado temporariamente

// Middleware de tratamento de erros (deve ser o último)
app.use(errorEventMiddleware);

// Inicialização dos serviços (MongoDB removido - usando Supabase)
async function initializeServices() {
  console.log('🚀 Banco de dados: Supabase (PostgreSQL)');

  // MongoDB removido - gestantes agora no Supabase
  console.log('📋 Gestantes cadastradas: Disponíveis via Supabase');

  // console.log('🚀 Iniciando Proactive Scheduler...');
  // await proactiveScheduler.start(); // Desabilitado temporariamente
  console.log('✅ Todos os serviços inicializados com sucesso!');
}

// FUNÇÃO REMOVIDA - MongoDB substituído por Supabase
// Gestantes agora são listadas via Supabase Dashboard ou APIs REST

initializeServices();

const PORT = process.env.PORT || 3334; // Porta configurável

httpServer.listen(PORT, () => {
  console.log(`Servidor rodando na porta ${PORT}`);
  console.log('Aguardando conexão do WhatsApp...');

  // Emitir evento de startup do sistema
  emitSystemStartup();
});

// Capturar sinais de shutdown para emitir evento
process.on('SIGTERM', () => {
  console.log('🛑 Recebido SIGTERM, encerrando servidor...');
  emitSystemShutdown('SIGTERM received');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Recebido SIGINT, encerrando servidor...');
  emitSystemShutdown('SIGINT received');
  process.exit(0);
});