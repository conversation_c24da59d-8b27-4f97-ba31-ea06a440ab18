// Script para verificar e corrigir dados do contato no Supabase
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis SUPABASE_URL e SUPABASE_ANON_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkAndFixContactData() {
  try {
    console.log('🔍 Verificando dados dos contatos no Supabase...\n');

    // Buscar todos os contatos
    const { data: contacts, error } = await supabase
      .from('contacts')
      .select('*')
      .order('last_interaction', { ascending: false });

    if (error) {
      console.error('❌ Erro ao buscar contatos:', error.message);
      return;
    }

    console.log(`📋 Encontrados ${contacts.length} contatos:\n`);

    // Mostrar dados atuais
    contacts.forEach((contact, index) => {
      console.log(`${index + 1}. ID: ${contact.id}`);
      console.log(`   Nome: "${contact.name}"`);
      console.log(`   Telefone: ${contact.phone}`);
      console.log(`   Gênero do bebê: ${contact.baby_gender}`);
      console.log(`   Status: ${contact.registration_status}`);
      console.log(`   Ativo: ${contact.is_active}`);
      console.log(`   Última interação: ${contact.last_interaction}`);
      console.log(`   Criado em: ${contact.created_at}\n`);
    });

    // Verificar se há contato com nome incorreto
    const problematicContact = contacts.find(c => 
      c.name.includes('Itala Raxa Cabra') || 
      c.phone === '(84) 98850-1582'
    );

    if (problematicContact) {
      console.log('🔧 Contato com dados incorretos encontrado!');
      console.log('📝 Dados atuais:', problematicContact);
      
      // Corrigir dados
      const { data: updatedContact, error: updateError } = await supabase
        .from('contacts')
        .update({
          name: 'Italo Cabral',
          baby_gender: 'male',
          last_interaction: new Date().toISOString()
        })
        .eq('id', problematicContact.id)
        .select()
        .single();

      if (updateError) {
        console.error('❌ Erro ao atualizar contato:', updateError.message);
      } else {
        console.log('✅ Contato corrigido com sucesso!');
        console.log('📝 Novos dados:', updatedContact);
      }
    } else {
      console.log('✅ Nenhum contato com dados incorretos encontrado.');
    }

    // Verificar mensagens relacionadas
    console.log('\n💬 Verificando mensagens...');
    const { data: messages, error: msgError } = await supabase
      .from('messages')
      .select('*')
      .order('timestamp', { ascending: false })
      .limit(10);

    if (msgError) {
      console.error('❌ Erro ao buscar mensagens:', msgError.message);
    } else {
      console.log(`📨 Últimas ${messages.length} mensagens:`);
      messages.forEach((msg, index) => {
        console.log(`${index + 1}. Contato: ${msg.contact_id}`);
        console.log(`   Conteúdo: "${msg.content.substring(0, 50)}..."`);
        console.log(`   De mim: ${msg.from_me}`);
        console.log(`   Timestamp: ${new Date(msg.timestamp * 1000).toLocaleString('pt-BR')}\n`);
      });
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar script
checkAndFixContactData()
  .then(() => {
    console.log('🎉 Script concluído!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
