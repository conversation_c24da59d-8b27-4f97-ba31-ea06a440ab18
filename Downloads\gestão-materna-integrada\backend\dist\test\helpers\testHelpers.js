"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.invalidUserData = exports.invalidContactData = exports.validUserData = exports.validContactData = exports.createUserWithPermissions = exports.getAuthHeaders = exports.createTestMessage = exports.createTestContact = exports.generateTestToken = exports.createTestAdmin = exports.createTestUser = exports.createTestApp = exports.createMockGeminiService = exports.createMockWhatsAppClient = exports.resetMockAuth = exports.setMockAuth = void 0;
const express_1 = __importDefault(require("express"));
const User_1 = require("../../models/User");
const Contact_1 = require("../../models/Contact");
const Message_1 = require("../../models/Message");
const routes_1 = require("../../routes");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
// Sistema de mock de autenticação mais seletivo
let mockAuthEnabled = true;
let mockUser = null;
let mockPermissions = [];
const setMockAuth = (enabled, user, permissions) => {
    mockAuthEnabled = enabled;
    mockUser = user;
    mockPermissions = permissions || [];
};
exports.setMockAuth = setMockAuth;
const resetMockAuth = () => {
    mockAuthEnabled = true;
    mockUser = {
        _id: 'test-user-id',
        email: '<EMAIL>',
        role: 'coordinator',
        permissions: ['read:contacts', 'write:messages', 'send:bulk_messages', 'read:analytics', 'manage:system'],
        isActive: true
    };
    mockPermissions = ['read:contacts', 'write:messages', 'send:bulk_messages', 'read:analytics', 'manage:system'];
};
exports.resetMockAuth = resetMockAuth;
// Mock dos middlewares de autenticação
jest.mock('../../middleware/auth', () => {
    const originalModule = jest.requireActual('../../middleware/auth');
    return {
        ...originalModule,
        authenticate: (req, res, next) => {
            if (!mockAuthEnabled) {
                return res.status(401).json({ error: 'Token não fornecido', code: 'NO_TOKEN' });
            }
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                return res.status(401).json({ error: 'Token inválido', code: 'INVALID_TOKEN' });
            }
            const token = authHeader.substring(7);
            if (token === 'token_invalido') {
                return res.status(401).json({ error: 'Token inválido', code: 'INVALID_TOKEN' });
            }
            req.user = mockUser || {
                _id: 'test-user-id',
                email: '<EMAIL>',
                role: 'coordinator',
                permissions: mockPermissions,
                isActive: true
            };
            next();
        },
        authorize: (permission) => (req, res, next) => {
            var _a, _b;
            if (!mockAuthEnabled) {
                return res.status(401).json({ error: 'Não autenticado', code: 'NOT_AUTHENTICATED' });
            }
            const userPermissions = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.permissions) || mockPermissions;
            const hasPermission = userPermissions.includes(permission) || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'admin';
            if (!hasPermission) {
                return res.status(403).json({
                    error: 'Permissão insuficiente',
                    code: 'INSUFFICIENT_PERMISSIONS',
                    required: permission,
                    current: userPermissions
                });
            }
            next();
        },
        authorizeRole: (roles) => (req, res, next) => {
            var _a;
            if (!mockAuthEnabled) {
                return res.status(401).json({ error: 'Não autenticado', code: 'NOT_AUTHENTICATED' });
            }
            const userRole = (_a = req.user) === null || _a === void 0 ? void 0 : _a.role;
            if (!roles.includes(userRole)) {
                return res.status(403).json({
                    error: 'Role insuficiente',
                    code: 'INSUFFICIENT_ROLE',
                    required: roles,
                    current: userRole
                });
            }
            next();
        },
        requireAdmin: (req, res, next) => {
            var _a;
            if (!mockAuthEnabled) {
                return res.status(401).json({ error: 'Não autenticado', code: 'NOT_AUTHENTICATED' });
            }
            const userRole = ((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) || (mockUser === null || mockUser === void 0 ? void 0 : mockUser.role);
            if (userRole !== 'admin') {
                return res.status(403).json({ error: 'Acesso restrito a administradores', code: 'ADMIN_REQUIRED' });
            }
            next();
        },
        requireAdminOrCoordinator: (req, res, next) => {
            var _a;
            if (!mockAuthEnabled || !['admin', 'coordinator'].includes((_a = req.user) === null || _a === void 0 ? void 0 : _a.role)) {
                return res.status(403).json({ error: 'Acesso restrito', code: 'ADMIN_OR_COORDINATOR_REQUIRED' });
            }
            next();
        },
        requireHealthProfessional: (req, res, next) => {
            var _a;
            if (!mockAuthEnabled || !['admin', 'coordinator', 'doctor', 'nurse'].includes((_a = req.user) === null || _a === void 0 ? void 0 : _a.role)) {
                return res.status(403).json({ error: 'Acesso restrito a profissionais de saúde', code: 'HEALTH_PROFESSIONAL_REQUIRED' });
            }
            next();
        },
        auditLog: (action) => (req, res, next) => {
            // Simular log de auditoria
            next();
        },
        optionalAuth: (req, res, next) => {
            // Simular autenticação opcional
            next();
        },
        rateLimitByUser: (maxRequests, windowMs) => (req, res, next) => {
            // Simular rate limiting
            next();
        },
        validateResourceOwnership: (resourceModel, resourceIdParam) => (req, res, next) => {
            // Simular validação de propriedade
            next();
        }
    };
});
// Mock dos serviços externos
const createMockWhatsAppClient = () => {
    return {
        sendMessage: jest.fn().mockResolvedValue(true),
        sendBulkMessages: jest.fn().mockResolvedValue([]),
        getStatus: jest.fn().mockResolvedValue({
            connected: true,
            authenticated: true,
            status: 'connected',
            connectionState: 'CONNECTED',
            qr: null,
            info: { user: '<EMAIL>' }
        }),
        initialize: jest.fn().mockResolvedValue(undefined),
        destroy: jest.fn().mockResolvedValue(undefined),
    };
};
exports.createMockWhatsAppClient = createMockWhatsAppClient;
const createMockGeminiService = () => {
    return {
        generateResponse: jest.fn().mockResolvedValue({
            message: 'Resposta de teste da IA',
            sentiment: 'positive'
        }),
        generateFollowUpMessage: jest.fn().mockResolvedValue('Mensagem de acompanhamento de teste'),
        analyzeSentiment: jest.fn().mockResolvedValue('positive'),
    };
};
exports.createMockGeminiService = createMockGeminiService;
// Criar aplicação Express para testes
const createTestApp = () => {
    const app = (0, express_1.default)();
    // Middlewares básicos
    app.use(express_1.default.json());
    app.use(express_1.default.urlencoded({ extended: true }));
    // Mocks dos serviços
    const mockWhatsAppClient = (0, exports.createMockWhatsAppClient)();
    const mockGeminiService = (0, exports.createMockGeminiService)();
    // Configurar rotas
    (0, routes_1.setupRoutes)(app, mockWhatsAppClient, mockGeminiService);
    return { app, mockWhatsAppClient, mockGeminiService };
};
exports.createTestApp = createTestApp;
// Criar usuário de teste
const createTestUser = async (userData = {}) => {
    const defaultUser = {
        name: 'Usuário Teste',
        email: '<EMAIL>',
        password: 'SenhaSegura123!',
        role: 'nurse'
    };
    const user = new User_1.User({ ...defaultUser, ...userData });
    await user.save();
    return user;
};
exports.createTestUser = createTestUser;
// Criar admin de teste
const createTestAdmin = async () => {
    return (0, exports.createTestUser)({
        name: 'Admin Teste',
        email: '<EMAIL>',
        role: 'admin'
    });
};
exports.createTestAdmin = createTestAdmin;
// Gerar token JWT para testes
const generateTestToken = (userId, role = 'nurse') => {
    return jsonwebtoken_1.default.sign({ userId, role }, process.env.JWT_SECRET || 'test_jwt_secret_key_for_testing_only', { expiresIn: '1h' });
};
exports.generateTestToken = generateTestToken;
// Criar contato de teste
const createTestContact = async (contactData = {}) => {
    // Gerar telefone único para evitar duplicatas
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const uniquePhone = `+5511${timestamp.toString().slice(-5)}${randomSuffix}`;
    const defaultContact = {
        name: 'Ana Silva',
        phone: uniquePhone,
        babyGender: 'female',
        isActive: true
    };
    const contact = new Contact_1.Contact({ ...defaultContact, ...contactData });
    await contact.save();
    return contact;
};
exports.createTestContact = createTestContact;
// Criar mensagem de teste
const createTestMessage = async (messageData = {}) => {
    let contact;
    // Se já foi fornecido um contact ID, usar ele, senão criar um novo
    if (messageData.contact) {
        contact = { _id: messageData.contact };
    }
    else {
        contact = await (0, exports.createTestContact)();
    }
    const defaultMessage = {
        contact: contact._id,
        content: 'Mensagem de teste',
        type: 'text', // Usar 'text' em vez de 'received'
        fromMe: false, // Campo obrigatório
        timestamp: new Date()
    };
    const message = new Message_1.Message({ ...defaultMessage, ...messageData });
    await message.save();
    return { message, contact };
};
exports.createTestMessage = createTestMessage;
// Headers de autenticação para testes
const getAuthHeaders = (token) => ({
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
});
exports.getAuthHeaders = getAuthHeaders;
// Criar usuário com permissões específicas para testes
const createUserWithPermissions = async (permissions, role = 'nurse') => {
    const timestamp = Date.now();
    const randomSuffix = Math.floor(Math.random() * 1000);
    const uniqueEmail = `user-${timestamp}-${randomSuffix}@exemplo.com`;
    const user = await (0, exports.createTestUser)({
        role,
        email: uniqueEmail
    });
    // Forçar permissões específicas (normalmente definidas pelo middleware do modelo)
    user.permissions = permissions;
    await user.save();
    return user;
};
exports.createUserWithPermissions = createUserWithPermissions;
// Dados de teste válidos
exports.validContactData = {
    name: 'Maria Santos',
    phone: '+5511888888888',
    babyGender: 'male'
};
exports.validUserData = {
    name: 'João Silva',
    email: '<EMAIL>',
    password: 'MinhaSenh@123',
    role: 'doctor'
};
// Dados de teste inválidos
exports.invalidContactData = {
    name: '', // Nome vazio
    phone: '123', // Telefone inválido
    babyGender: 'invalid' // Gênero inválido
};
exports.invalidUserData = {
    name: 'A', // Nome muito curto
    email: 'email-invalido', // Email inválido
    password: '123', // Senha muito simples
    role: 'invalid' // Role inválido
};
