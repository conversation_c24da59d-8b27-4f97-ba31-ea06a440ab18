"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const whatsappAutoStart_1 = require("../services/whatsappAutoStart");
const wppConnectIntegration_1 = require("../services/wppConnectIntegration");
const audioQueue_1 = require("../services/audioQueue");
const router = (0, express_1.Router)();
/**
 * GET /api/whatsapp-auto/status
 * Obter status completo do WhatsApp automático
 */
router.get('/status', async (req, res) => {
    try {
        const status = whatsappAutoStart_1.whatsappAutoStart.getStatus();
        res.json({
            success: true,
            data: status,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter status do WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: 'Erro interno do servidor',
            message: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/start
 * Iniciar WhatsApp automático
 */
router.post('/start', async (req, res) => {
    try {
        console.log('🚀 Iniciando WhatsApp automático via API...');
        const success = await whatsappAutoStart_1.whatsappAutoStart.start();
        if (success) {
            res.json({
                success: true,
                message: 'WhatsApp automático iniciado com sucesso',
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(500).json({
                success: false,
                error: 'Falha ao iniciar WhatsApp automático'
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao iniciar WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/stop
 * Parar WhatsApp automático
 */
router.post('/stop', async (req, res) => {
    try {
        console.log('🛑 Parando WhatsApp automático via API...');
        await whatsappAutoStart_1.whatsappAutoStart.stop();
        res.json({
            success: true,
            message: 'WhatsApp automático parado com sucesso',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao parar WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/restart
 * Reiniciar WhatsApp automático
 */
router.post('/restart', async (req, res) => {
    try {
        console.log('🔄 Reiniciando WhatsApp automático via API...');
        const success = await whatsappAutoStart_1.whatsappAutoStart.restart();
        if (success) {
            res.json({
                success: true,
                message: 'WhatsApp automático reiniciado com sucesso',
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(500).json({
                success: false,
                error: 'Falha ao reiniciar WhatsApp automático'
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao reiniciar WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/send-test
 * Enviar mensagem de teste
 */
router.post('/send-test', async (req, res) => {
    try {
        const { phoneNumber } = req.body;
        if (!phoneNumber) {
            return res.status(400).json({
                success: false,
                error: 'Número de telefone é obrigatório'
            });
        }
        console.log(`📤 Enviando mensagem de teste para ${phoneNumber}...`);
        const success = await whatsappAutoStart_1.whatsappAutoStart.sendTestMessage(phoneNumber);
        if (success) {
            res.json({
                success: true,
                message: `Mensagem de teste enviada para ${phoneNumber}`,
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(500).json({
                success: false,
                error: 'Falha ao enviar mensagem de teste'
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao enviar mensagem de teste:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/test-audio
 * Testar processamento de áudio
 */
router.post('/test-audio', async (req, res) => {
    try {
        console.log('🧪 Testando processamento de áudio...');
        const jobId = await whatsappAutoStart_1.whatsappAutoStart.testAudioProcessing();
        res.json({
            success: true,
            message: 'Teste de processamento de áudio iniciado',
            data: {
                jobId,
                queueStats: audioQueue_1.audioQueue.getStats()
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro no teste de áudio:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * GET /api/whatsapp-auto/connection
 * Obter status de conexão detalhado
 */
router.get('/connection', async (req, res) => {
    try {
        const connectionStatus = wppConnectIntegration_1.wppConnectIntegration.getConnectionStatus();
        const stats = wppConnectIntegration_1.wppConnectIntegration.getStats();
        res.json({
            success: true,
            data: {
                connection: connectionStatus,
                stats: stats,
                uptime: process.uptime(),
                memory: process.memoryUsage()
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter status de conexão:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * POST /api/whatsapp-auto/send-message
 * Enviar mensagem personalizada
 */
router.post('/send-message', async (req, res) => {
    try {
        const { phoneNumber, message } = req.body;
        if (!phoneNumber || !message) {
            return res.status(400).json({
                success: false,
                error: 'Número de telefone e mensagem são obrigatórios'
            });
        }
        console.log(`📤 Enviando mensagem personalizada para ${phoneNumber}...`);
        const success = await wppConnectIntegration_1.wppConnectIntegration.sendText(phoneNumber, message);
        if (success) {
            res.json({
                success: true,
                message: `Mensagem enviada para ${phoneNumber}`,
                data: {
                    to: phoneNumber,
                    content: message
                },
                timestamp: new Date().toISOString()
            });
        }
        else {
            res.status(500).json({
                success: false,
                error: 'Falha ao enviar mensagem'
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao enviar mensagem:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
/**
 * GET /api/whatsapp-auto/health
 * Health check do sistema WhatsApp
 */
router.get('/health', async (req, res) => {
    try {
        const status = whatsappAutoStart_1.whatsappAutoStart.getStatus();
        const connectionStatus = wppConnectIntegration_1.wppConnectIntegration.getConnectionStatus();
        const queueStats = audioQueue_1.audioQueue.getStats();
        const isHealthy = status.isRunning &&
            connectionStatus.isConnected &&
            connectionStatus.isAuthenticated &&
            queueStats.processing.successRate > 80;
        res.status(isHealthy ? 200 : 503).json({
            success: true,
            healthy: isHealthy,
            data: {
                service: {
                    running: status.isRunning,
                    uptime: status.uptime
                },
                connection: {
                    connected: connectionStatus.isConnected,
                    authenticated: connectionStatus.isAuthenticated,
                    phone: connectionStatus.phoneNumber
                },
                queue: {
                    pending: queueStats.queue.pending,
                    processing: queueStats.queue.processing,
                    successRate: queueStats.processing.successRate
                }
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro no health check:', error);
        res.status(500).json({
            success: false,
            healthy: false,
            error: error.message
        });
    }
});
/**
 * GET /api/whatsapp-auto/logs
 * Obter logs recentes (desenvolvimento)
 */
router.get('/logs', async (req, res) => {
    try {
        if (process.env.NODE_ENV === 'production') {
            return res.status(403).json({
                success: false,
                error: 'Endpoint de logs não disponível em produção'
            });
        }
        // Simular logs recentes
        const logs = [
            {
                timestamp: new Date().toISOString(),
                level: 'info',
                message: 'Sistema funcionando normalmente',
                service: 'whatsapp-auto'
            },
            {
                timestamp: new Date(Date.now() - 60000).toISOString(),
                level: 'info',
                message: 'Áudio processado com sucesso',
                service: 'audio-queue'
            },
            {
                timestamp: new Date(Date.now() - 120000).toISOString(),
                level: 'info',
                message: 'Mensagem enviada automaticamente',
                service: 'wpp-connect'
            }
        ];
        res.json({
            success: true,
            data: {
                logs,
                count: logs.length
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('❌ Erro ao obter logs:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});
exports.default = router;
