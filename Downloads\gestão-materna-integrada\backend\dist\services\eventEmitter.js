"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemWebhookEvents = exports.systemEventEmitter = exports.SystemEventEmitter = void 0;
const webhookService_1 = require("./webhookService");
Object.defineProperty(exports, "SystemWebhookEvents", { enumerable: true, get: function () { return webhookService_1.SystemWebhookEvents; } });
/**
 * Serviço centralizado para emissão de eventos do sistema
 * Facilita a emissão de webhooks para todos os eventos importantes
 */
class SystemEventEmitter {
    constructor() { }
    static getInstance() {
        if (!SystemEventEmitter.instance) {
            SystemEventEmitter.instance = new SystemEventEmitter();
        }
        return SystemEventEmitter.instance;
    }
    // Eventos de Contato
    emitContactCreated(contactData) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.CONTACT_CREATED, {
            contact: contactData,
            action: 'created'
        }, 'system', 'medium');
    }
    emitContactUpdated(contactId, changes) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.CONTACT_UPDATED, {
            contact_id: contactId,
            changes,
            action: 'updated'
        }, 'system', 'low');
    }
    emitContactDeleted(contactId) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.CONTACT_DELETED, {
            contact_id: contactId,
            action: 'deleted'
        }, 'system', 'medium');
    }
    // Eventos de Agendamento
    emitScheduleCreated(scheduleData) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.SCHEDULE_CREATED, {
            schedule: scheduleData,
            action: 'created'
        }, 'scheduler', 'medium');
    }
    emitScheduleExecuted(scheduleId, result) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.SCHEDULE_EXECUTED, {
            schedule_id: scheduleId,
            result,
            action: 'executed'
        }, 'scheduler', 'low');
    }
    // Eventos de IA
    emitAIResponseGenerated(contactId, message, response) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.AI_RESPONSE_GENERATED, {
            contact_id: contactId,
            original_message: message,
            ai_response: response,
            action: 'ai_response'
        }, 'ai', 'low');
    }
    emitSentimentAnalyzed(contactId, analysis) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.SENTIMENT_ANALYZED, {
            contact_id: contactId,
            analysis,
            action: 'sentiment_analysis'
        }, 'ai', analysis.priority === 'alta' ? 'high' : 'low');
    }
    // Eventos de Sistema
    emitSystemStartup(serverInfo) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.SYSTEM_STARTUP, {
            server_info: serverInfo,
            startup_time: new Date(),
            action: 'startup'
        }, 'system', 'medium');
    }
    emitSystemShutdown(reason) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.SYSTEM_SHUTDOWN, {
            reason: reason || 'Normal shutdown',
            shutdown_time: new Date(),
            action: 'shutdown'
        }, 'system', 'high');
    }
    emitBulkMessageCompleted(stats) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.BULK_MESSAGE_COMPLETED, {
            stats,
            completion_time: new Date(),
            action: 'bulk_completed'
        }, 'whatsapp', 'medium');
    }
    emitTemplateUsed(templateId, contactId) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.TEMPLATE_USED, {
            template_id: templateId,
            contact_id: contactId,
            used_at: new Date(),
            action: 'template_used'
        }, 'system', 'low');
    }
    // Eventos de Autenticação
    emitUserLogin(userId, userInfo) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.USER_LOGIN, {
            user_id: userId,
            user_info: userInfo,
            login_time: new Date(),
            action: 'login'
        }, 'auth', 'low');
    }
    emitUserLogout(userId) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.USER_LOGOUT, {
            user_id: userId,
            logout_time: new Date(),
            action: 'logout'
        }, 'auth', 'low');
    }
    // Eventos de Dados
    emitDataExport(userId, exportType, recordCount) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.DATA_EXPORT, {
            user_id: userId,
            export_type: exportType,
            record_count: recordCount,
            export_time: new Date(),
            action: 'data_export'
        }, 'data', 'medium');
    }
    emitDataImport(userId, importType, stats) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.DATA_IMPORT, {
            user_id: userId,
            import_type: importType,
            stats,
            import_time: new Date(),
            action: 'data_import'
        }, 'data', 'medium');
    }
    // Eventos de Saúde
    emitHealthCheckFailed(checkId, error) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.HEALTH_CHECK_FAILED, {
            check_id: checkId,
            error,
            failed_at: new Date(),
            action: 'health_check_failed'
        }, 'health', 'high');
    }
    emitResourceWarning(resource, details) {
        this.emitEvent(webhookService_1.SystemWebhookEvents.RESOURCE_WARNING, {
            resource,
            details,
            warning_time: new Date(),
            action: 'resource_warning'
        }, 'health', 'high');
    }
    // Método privado para emitir eventos
    emitEvent(type, data, source, priority = 'medium') {
        try {
            const event = {
                id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                type,
                timestamp: new Date(),
                data,
                source,
                priority
            };
            // webhookService.emitWebhookEvent(event); // Desabilitado temporariamente
            // Log baseado na prioridade
            if (priority === 'critical' || priority === 'high') {
                console.warn(`🚨 Evento ${priority.toUpperCase()}: ${type}`, data);
            }
            else {
                console.log(`📡 Evento emitido: ${type}`, { source, priority });
            }
        }
        catch (error) {
            console.error('❌ Erro ao emitir evento:', error);
        }
    }
}
exports.SystemEventEmitter = SystemEventEmitter;
// Instância singleton para uso global
exports.systemEventEmitter = SystemEventEmitter.getInstance();
