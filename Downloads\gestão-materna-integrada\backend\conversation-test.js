/**
 * TESTE DE CONVERSAS - SISTEMA RAFAELA
 * Testa especificamente o histórico de conversas e funcionalidades de chat
 */

const https = require('https');
const http = require('http');

const API_BASE = 'http://localhost:3334/api';

// Função para fazer requisição HTTP
function fazerRequisicao(url, method = 'GET', body = null) {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const httpModule = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
      
      const req = httpModule.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            resolve({ 
              success: res.statusCode >= 200 && res.statusCode < 300, 
              data: jsonData, 
              status: res.statusCode 
            });
          } catch (error) {
            resolve({ success: false, error: 'Invalid JSON response', rawData: data });
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({ success: false, error: error.message });
      });
      
      if (body) {
        req.write(JSON.stringify(body));
      }
      
      req.end();
    } catch (error) {
      resolve({ success: false, error: error.message });
    }
  });
}

// Função para aguardar
function aguardar(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Mensagens de teste para conversa
const conversaTeste = [
  { fromMe: false, content: 'Oi! Como você está hoje?' },
  { fromMe: true, content: 'Olá! Estou bem, obrigada por perguntar. Como posso ajudar você hoje?' },
  { fromMe: false, content: 'Estou sentindo o bebê mexer muito! É normal?' },
  { fromMe: true, content: 'Que alegria! Sim, é completamente normal sentir o bebê mexendo bastante. Isso mostra que ele está ativo e saudável. Em que semana você está?' },
  { fromMe: false, content: 'Estou na 28ª semana. Às vezes ele chuta tanto que não consigo dormir!' },
  { fromMe: true, content: 'Na 28ª semana é muito comum mesmo! O bebê está crescendo e tem menos espaço, então os movimentos ficam mais intensos. Tente deitar do lado esquerdo, isso pode ajudar.' },
  { fromMe: false, content: 'Obrigada pela dica! Vocês são muito atenciosos 💝' },
  { fromMe: true, content: 'Fico feliz em ajudar! Estamos aqui para cuidar de você e do seu bebê. Qualquer dúvida, pode falar conosco!' }
];

// Função principal de teste
async function testarConversas() {
  console.log('💬 INICIANDO TESTE DE CONVERSAS - SISTEMA RAFAELA');
  console.log('=' .repeat(60));
  
  try {
    // 1. Buscar gestantes disponíveis
    console.log('\n👥 Buscando gestantes...');
    await aguardar(3000); // Aguardar para evitar rate limit
    
    const gestantesResult = await fazerRequisicao(`${API_BASE}/contacts`);
    
    if (!gestantesResult.success) {
      console.log('❌ Erro ao buscar gestantes:', gestantesResult.error);
      return;
    }
    
    const gestantes = gestantesResult.data;
    console.log(`✅ ${gestantes.length} gestantes encontradas`);
    
    if (gestantes.length === 0) {
      console.log('❌ Nenhuma gestante disponível para teste');
      return;
    }
    
    // 2. Selecionar primeira gestante para teste
    const gestante = gestantes[0];
    console.log(`\n🎯 Testando com: ${gestante.name}`);
    
    // 3. Verificar histórico existente
    console.log('\n📚 Verificando histórico existente...');
    await aguardar(2000);
    
    const historicoResult = await fazerRequisicao(`${API_BASE}/contacts/${gestante.id}/messages`);
    
    if (historicoResult.success) {
      const mensagensExistentes = historicoResult.data.data || [];
      console.log(`📱 ${mensagensExistentes.length} mensagens no histórico`);
      
      if (mensagensExistentes.length > 0) {
        console.log('📋 Últimas mensagens:');
        mensagensExistentes.slice(-3).forEach((msg, index) => {
          const remetente = msg.from_me ? 'Você' : gestante.name;
          const conteudo = msg.content.substring(0, 50) + (msg.content.length > 50 ? '...' : '');
          console.log(`  ${index + 1}. [${remetente}] ${conteudo}`);
        });
      }
    } else {
      console.log('❌ Erro ao buscar histórico:', historicoResult.error);
    }
    
    // 4. Simular conversa completa
    console.log('\n💬 Simulando conversa completa...');
    let mensagensCriadas = 0;
    let erros = 0;
    
    for (let i = 0; i < conversaTeste.length; i++) {
      const mensagem = conversaTeste[i];
      const remetente = mensagem.fromMe ? 'Você' : gestante.name;
      
      console.log(`📝 ${i + 1}/${conversaTeste.length} [${remetente}] ${mensagem.content.substring(0, 40)}...`);
      
      await aguardar(1500); // Pausa realística entre mensagens
      
      const resultado = await fazerRequisicao(
        `${API_BASE}/contacts/${gestante.id}/messages`,
        'POST',
        {
          content: mensagem.content,
          type: 'text',
          fromMe: mensagem.fromMe
        }
      );
      
      if (resultado.success) {
        mensagensCriadas++;
        console.log('  ✅ Mensagem salva no histórico');
      } else {
        erros++;
        console.log('  ❌ Erro ao salvar:', resultado.error);
      }
    }
    
    console.log(`\n📊 Conversa simulada: ${mensagensCriadas}/${conversaTeste.length} mensagens salvas`);
    
    // 5. Verificar histórico atualizado
    console.log('\n🔍 Verificando histórico atualizado...');
    await aguardar(2000);
    
    const historicoFinalResult = await fazerRequisicao(`${API_BASE}/contacts/${gestante.id}/messages`);
    
    if (historicoFinalResult.success) {
      const mensagensFinal = historicoFinalResult.data.data || [];
      console.log(`📱 ${mensagensFinal.length} mensagens no histórico final`);
      
      // Mostrar últimas 5 mensagens
      console.log('\n📋 Últimas 5 mensagens do histórico:');
      mensagensFinal.slice(-5).forEach((msg, index) => {
        const remetente = msg.from_me ? 'Você' : gestante.name;
        const timestamp = new Date(msg.timestamp).toLocaleTimeString('pt-BR');
        console.log(`  ${index + 1}. [${timestamp}] [${remetente}] ${msg.content}`);
      });
    }
    
    // 6. Testar IA com contexto da conversa
    console.log('\n🤖 Testando IA com contexto da conversa...');
    await aguardar(2000);
    
    const contexto = conversaTeste.map(m => `${m.fromMe ? 'Equipe' : 'Gestante'}: ${m.content}`).join('\n');
    
    const iaResult = await fazerRequisicao(
      `${API_BASE}/ai/generate-suggestion`,
      'POST',
      {
        prompt: 'Com base na conversa acima, sugira uma resposta empática e útil',
        systemInstruction: 'Você é Rafaela, assistente virtual para gestantes. Seja carinhosa e prestativa.',
        promptType: 'empathy',
        context: contexto
      }
    );
    
    if (iaResult.success) {
      console.log('✅ IA gerou sugestão:');
      console.log(`"${iaResult.data.suggestion}"`);
      
      // Salvar sugestão da IA como mensagem
      await aguardar(1000);
      const salvandoIA = await fazerRequisicao(
        `${API_BASE}/contacts/${gestante.id}/messages`,
        'POST',
        {
          content: iaResult.data.suggestion,
          type: 'text',
          fromMe: true
        }
      );
      
      if (salvandoIA.success) {
        console.log('✅ Sugestão da IA salva no histórico');
      }
    } else {
      console.log('❌ Erro na IA:', iaResult.error);
    }
    
    // 7. Testar marcação como lida
    console.log('\n👁️ Testando marcação de mensagens como lidas...');
    await aguardar(1500);
    
    const marcarLidasResult = await fazerRequisicao(
      `${API_BASE}/contacts/${gestante.id}/messages/read`,
      'PATCH'
    );
    
    if (marcarLidasResult.success) {
      console.log('✅ Mensagens marcadas como lidas');
    } else {
      console.log('❌ Erro ao marcar como lidas:', marcarLidasResult.error);
    }
    
    // Relatório final
    console.log('\n🎉 TESTE DE CONVERSAS CONCLUÍDO!');
    console.log('=' .repeat(60));
    console.log(`👥 Gestante testada: ${gestante.name}`);
    console.log(`💬 Mensagens criadas: ${mensagensCriadas}/${conversaTeste.length}`);
    console.log(`🤖 IA funcionando: ${iaResult.success ? '✅' : '❌'}`);
    console.log(`📚 Histórico funcionando: ${historicoFinalResult.success ? '✅' : '❌'}`);
    console.log(`👁️ Marcar como lida: ${marcarLidasResult.success ? '✅' : '❌'}`);
    
    const sistemaOk = mensagensCriadas >= conversaTeste.length * 0.8 && 
                     iaResult.success && 
                     historicoFinalResult.success;
    
    console.log(`\n🎯 AVALIAÇÃO FINAL: ${sistemaOk ? '✅ SISTEMA APROVADO' : '⚠️ SISTEMA PRECISA AJUSTES'}`);
    
    if (sistemaOk) {
      console.log('🎊 O histórico de conversas está funcionando perfeitamente!');
      console.log('💬 Pronto para uso em produção com gestantes reais');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  testarConversas()
    .catch(error => {
      console.error('❌ Erro no teste:', error);
    });
}

module.exports = { testarConversas };
