"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const http_1 = require("http");
const socket_io_1 = require("socket.io");
const dotenv_1 = __importDefault(require("dotenv"));
const whatsapp_1 = require("./services/whatsapp");
const gemini_1 = require("./services/gemini");
const whatsappAutoStart_1 = require("./services/whatsappAutoStart");
// import ProactiveSchedulerService from './services/proactiveScheduler'; // Desabilitado temporariamente
const routes_1 = require("./routes");
// import { setupWebhooks } from './webhooks'; // Desabilitado temporariamente
// MongoDB removido - usando Supabase
// import { connectDatabase } from './config/database';
const eventCapture_1 = require("./middleware/eventCapture");
dotenv_1.default.config();
const app = (0, express_1.default)();
const httpServer = (0, http_1.createServer)(app);
const io = new socket_io_1.Server(httpServer, {
    cors: {
        origin: [
            'http://localhost:5173',
            'http://localhost:5174',
            'http://localhost:5175',
            'http://localhost:3000',
            process.env.FRONTEND_URL || 'http://localhost:5173'
        ],
        methods: ['GET', 'POST']
    }
});
// Middlewares de segurança
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", "data:", "https:"],
        },
    },
    crossOriginEmbedderPolicy: false
}));
// Rate limiting global
const limiter = (0, express_rate_limit_1.default)({
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutos
    max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // máximo 100 requests por IP
    message: {
        error: 'Muitas requisições deste IP, tente novamente mais tarde.',
        code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
});
app.use(limiter);
// CORS configurado para múltiplas portas
app.use((0, cors_1.default)({
    origin: [
        'http://localhost:5173',
        'http://localhost:5174',
        'http://localhost:5175',
        'http://localhost:3000',
        process.env.CORS_ORIGIN || 'http://localhost:5173'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
// Body parsing
app.use(express_1.default.json({
    limit: process.env.MAX_FILE_SIZE || '10mb',
    verify: (req, res, buf) => {
        // Verificação adicional de segurança para JSON
        try {
            JSON.parse(buf.toString());
        }
        catch (e) {
            throw new Error('JSON inválido');
        }
    }
}));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
// Middleware de captura de eventos
app.use(eventCapture_1.eventCaptureMiddleware);
// Inicialização dos serviços
const geminiService = new gemini_1.GeminiAIService();
const whatsappClient = new whatsapp_1.WhatsAppClient(io, geminiService);
// const proactiveScheduler = new ProactiveSchedulerService(whatsappClient, geminiService); // Desabilitado temporariamente
// Inicializar WhatsApp automático (wppConnect integração)
console.log('🚀 Iniciando WhatsApp automático...');
whatsappAutoStart_1.whatsappAutoStart.start().then(success => {
    if (success) {
        console.log('✅ WhatsApp automático iniciado com sucesso!');
    }
    else {
        console.warn('⚠️ WhatsApp automático falhou, usando método tradicional');
    }
}).catch(error => {
    console.error('❌ Erro no WhatsApp automático:', error);
    console.log('🔄 Continuando com método tradicional...');
});
// Configuração das rotas e webhooks
(0, routes_1.setupRoutes)(app, whatsappClient, geminiService);
// setupWebhooks(app, whatsappClient, geminiService); // Desabilitado temporariamente
// Middleware de tratamento de erros (deve ser o último)
app.use(eventCapture_1.errorEventMiddleware);
// Inicialização dos serviços (MongoDB removido - usando Supabase)
async function initializeServices() {
    console.log('🚀 Banco de dados: Supabase (PostgreSQL)');
    // MongoDB removido - gestantes agora no Supabase
    console.log('📋 Gestantes cadastradas: Disponíveis via Supabase');
    // console.log('🚀 Iniciando Proactive Scheduler...');
    // await proactiveScheduler.start(); // Desabilitado temporariamente
    console.log('✅ Todos os serviços inicializados com sucesso!');
}
// FUNÇÃO REMOVIDA - MongoDB substituído por Supabase
// Gestantes agora são listadas via Supabase Dashboard ou APIs REST
initializeServices();
const PORT = process.env.PORT || 3334; // Porta configurável
httpServer.listen(PORT, () => {
    console.log(`Servidor rodando na porta ${PORT}`);
    console.log('Aguardando conexão do WhatsApp...');
    // Emitir evento de startup do sistema
    (0, eventCapture_1.emitSystemStartup)();
});
// Capturar sinais de shutdown para emitir evento
process.on('SIGTERM', () => {
    console.log('🛑 Recebido SIGTERM, encerrando servidor...');
    (0, eventCapture_1.emitSystemShutdown)('SIGTERM received');
    process.exit(0);
});
process.on('SIGINT', () => {
    console.log('🛑 Recebido SIGINT, encerrando servidor...');
    (0, eventCapture_1.emitSystemShutdown)('SIGINT received');
    process.exit(0);
});
