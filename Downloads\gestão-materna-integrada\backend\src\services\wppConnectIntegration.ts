import * as wppconnect from '@wppconnect-team/wppconnect';
import { AutomaticWhatsAppHandler } from './whatsappAudioHandler';
import { audioQueue } from './audioQueue';
import { GeminiAIService } from './gemini';
import { contactService } from './contactService';
import { messageService } from './messageService';

interface WppConnectConfig {
  session: string;
  headless: boolean;
  devtools: boolean;
  useChrome: boolean;
  debug: boolean;
  logQR: boolean;
  autoClose: number;
  puppeteerOptions: {
    userDataDir: string;
    args?: string[];
  };
}

interface ConnectionStatus {
  isConnected: boolean;
  isAuthenticated: boolean;
  sessionStatus: string;
  phoneNumber?: string;
  deviceInfo?: any;
  lastActivity?: Date;
}

/**
 * Integração completa com wppConnect para processamento automático de áudios
 * Gerencia conexão, autenticação e processamento de mensagens
 */
export class WppConnectIntegration {
  private client: any = null;
  private automaticHandler: AutomaticWhatsAppHandler | null = null;
  private geminiService: GeminiAIService;
  private connectionStatus: ConnectionStatus;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectInterval: NodeJS.Timeout | null = null;
  private isInitializing: boolean = false;
  private keepAliveInterval: NodeJS.Timeout | null = null; // Para manter a sessão ativa

  constructor(geminiService: GeminiAIService) {
    this.geminiService = geminiService;
    this.connectionStatus = {
      isConnected: false,
      isAuthenticated: false,
      sessionStatus: 'disconnected'
    };
  }

  /**
   * Inicializar conexão com WhatsApp
   */
  public async initialize(): Promise<boolean> {
    if (this.isInitializing) {
      console.log('⏳ Inicialização já em andamento...');
      return false;
    }

    this.isInitializing = true;

    try {
      console.log('🚀 Iniciando integração wppConnect...');

      const config: WppConnectConfig = {
        session: 'rafaela-audio-session',
        headless: true,
        devtools: false,
        useChrome: false, // MUDANÇA: Usar Chromium padrão em vez de Chrome
        debug: false,
        logQR: true,
        autoClose: 0,
        disableSpins: true,
        disableWelcome: true,
        updatesLog: false,
        puppeteerOptions: {
          userDataDir: './tokens/rafaela-audio',
          executablePath: undefined, // Deixar o Puppeteer escolher automaticamente
          args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--single-process', // ADICIONADO: Força processo único
            '--no-default-browser-check',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-translate',
            '--disable-background-networking',
            '--disable-sync'
          ],
          handleSIGINT: false,
          handleSIGTERM: false,
          handleSIGHUP: false
        }
      };

      this.client = await wppconnect.create({
        ...config,
        catchQR: this.handleQRCode.bind(this),
        statusFind: this.handleStatusChange.bind(this),
        onLoadingScreen: this.handleLoadingScreen.bind(this)
      });

      if (this.client) {
        await this.setupEventListeners();
        await this.initializeAutomaticHandler();
        this.startKeepAlive(); // ATUALIZADO: Inicia o keep-alive para estabilidade
        
        console.log('✅ wppConnect integração inicializada com sucesso!');
        this.isInitializing = false;
        return true;
      }

      throw new Error('Cliente wppConnect não foi criado');

    } catch (error: any) {
      console.error('❌ Erro ao inicializar wppConnect:', error);
      this.connectionStatus.sessionStatus = 'error';
      this.isInitializing = false;
      
      this.scheduleReconnect();
      return false;
    }
  }

  /**
   * Configurar listeners de eventos
   */
  private async setupEventListeners(): Promise<void> {
    if (!this.client) return;

    console.log('🎧 Configurando listeners de eventos...');

    this.client.onMessage(async (message: any) => {
      try {
        await this.handleIncomingMessage(message);
      } catch (error) {
        console.error('❌ Erro ao processar mensagem:', error);
      }
    });

    this.client.onStateChange((state: string) => {
      console.log('📱 Estado alterado:', state);
      this.connectionStatus.sessionStatus = state;
      this.connectionStatus.lastActivity = new Date();
    });

    this.client.onAck((ack: any) => {
      // Opcional: Descomente para logs muito detalhados de ACK
      // console.log('✅ ACK recebido:', ack.id, ack.ack);
    });

    this.client.onPresenceChanged((presence: any) => {
      // Opcional: Descomente para logs de presença
      // console.log('👤 Presença alterada:', presence.id, presence.isOnline);
    });

    console.log('✅ Listeners configurados com sucesso');
  }
  
  /**
   * NOVO: Iniciar verificação periódica para manter a sessão ativa
   */
  private startKeepAlive(): void {
    if (this.keepAliveInterval) {
      clearInterval(this.keepAliveInterval);
    }

    this.keepAliveInterval = setInterval(async () => {
      if (this.client && this.connectionStatus.isConnected) {
        try {
          // Chama uma função leve para manter a sessão ativa
          const battery = await this.client.getBatteryLevel();
          console.log(`🔋 Keep-alive: Sessão ativa. Bateria do celular: ${battery}%`);
        } catch (error) {
          console.warn('⚠️ Erro no keep-alive (pode indicar desconexão, tentando reconectar):', error);
          this.scheduleReconnect();
        }
      }
    }, 4 * 60 * 1000); // A cada 4 minutos
  }

  /**
   * Processar mensagem recebida
   */
  private async handleIncomingMessage(message: any): Promise<void> {
    try {
      if (message.fromMe) {
        return;
      }

      this.connectionStatus.lastActivity = new Date();

      console.log('📨 Mensagem recebida:', {
        from: message.from,
        type: message.type,
        id: message.id,
        timestamp: new Date(message.timestamp * 1000).toISOString(),
        hasBody: !!message.body,
        hasMedia: !!message.media
      });

      // Salvar mensagem no histórico primeiro (para todas as mensagens válidas)
      if (message.body || message.media) {
        await this.saveMessageToHistory(message);
      }

      // PRIORIDADE PARA ÁUDIO - Verificar múltiplos tipos
      if (message.type === 'ptt' || message.type === 'audio' || message.type === 'voice') {
        console.log('🎵 ÁUDIO DETECTADO! Processando...');
        await this.processAudioMessage(message);
      } else if (message.type === 'chat' && message.body) {
        console.log('💬 TEXTO DETECTADO! Processando...');
        await this.processTextMessage(message);
      } else {
        console.log(`📝 Tipo de mensagem não processado: ${message.type}`, {
          hasBody: !!message.body,
          hasMedia: !!message.media,
          mimetype: message.mimetype
        });
      }

    } catch (error) {
      console.error('❌ Erro ao processar mensagem recebida:', error);
    }
  }

  /**
   * Processar mensagem de áudio
   */
  private async processAudioMessage(message: any): Promise<void> {
    try {
      console.log('🎵 INICIANDO PROCESSAMENTO DE ÁUDIO...');
      console.log('📋 Detalhes da mensagem de áudio:', {
        type: message.type,
        id: message.id,
        from: message.from,
        hasMedia: !!message.media,
        mimetype: message.mimetype
      });

      // Tentar primeiro com handler automático
      if (this.automaticHandler) {
        console.log('🤖 Tentando com handler automático...');
        try {
          const processed = await this.automaticHandler.processAudioMessage(message);
          if (processed) {
            console.log('✅ Áudio enfileirado pelo handler automático');
            return;
          }
        } catch (handlerError) {
          console.warn('⚠️ Erro no handler automático:', handlerError);
        }
      }

      // Fallback: processamento direto
      console.warn('🔄 Usando processamento direto de áudio...');
      await this.processAudioDirect(message);

    } catch (error) {
      console.error('❌ ERRO CRÍTICO no processamento de áudio:', error);
      await this.sendErrorMessage(message.from, '🎵 Desculpe, tive problemas para processar seu áudio. Tente enviar novamente ou digite sua mensagem.');
    }
  }

  /**
   * Processar áudio diretamente (fallback)
   */
  private async processAudioDirect(message: any): Promise<void> {
    try {
      const audioResult = await this.downloadAudioWithRetry(message);
      if (!audioResult) {
        throw new Error('Não foi possível baixar o áudio após múltiplas tentativas');
      }

      const { buffer: audioBuffer, mimeType } = audioResult;

      console.log('📥 Áudio baixado (fallback):', {
        size: audioBuffer.length,
        mimeType,
        messageId: message.id
      });

      const jobId = await audioQueue.addAudioJob(
        message.from,
        audioBuffer,
        mimeType,
        message.id,
        'high'
      );

      console.log(`📋 Áudio adicionado à fila via fallback: ${jobId}`);
      await this.sendText(message.from, '🎵 Recebi seu áudio! Estou processando...');

    } catch (error) {
      console.error('❌ Erro no processamento direto de áudio:', error);
      throw error;
    }
  }

  /**
   * Baixar áudio com múltiplas tentativas - VERSÃO MELHORADA
   */
  private async downloadAudioWithRetry(message: any, maxRetries: number = 3): Promise<{ buffer: Buffer; mimeType: string } | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📥 [wppConnect] Tentativa ${attempt}/${maxRetries} de download do áudio...`);
        console.log(`🔍 [wppConnect] Dados da mensagem:`, {
          id: message.id,
          type: message.type,
          hasMedia: !!message.media,
          from: message.from,
          mimetype: message.mimetype
        });

        // Método 1: downloadMedia
        let audioData = null;
        try {
          audioData = await this.client.downloadMedia(message);
          console.log(`📥 [wppConnect] downloadMedia resultado:`, {
            type: typeof audioData,
            isNull: audioData === null,
            length: audioData?.length || 'N/A'
          });
        } catch (downloadError) {
          console.warn(`⚠️ [wppConnect] downloadMedia falhou:`, downloadError.message);
        }

        // Método 2: Tentar métodos alternativos se necessário
        if (!audioData || (typeof audioData === 'string' && audioData.length < 100)) {
          console.log(`🔄 [wppConnect] Tentando métodos alternativos...`);

          try {
            if (this.client.decryptFile && message.deprecatedMms3Url) {
              audioData = await this.client.decryptFile(message);
            } else if (this.client.getMessageMedia) {
              audioData = await this.client.getMessageMedia(message.id);
            }
          } catch (altError) {
            console.warn(`⚠️ [wppConnect] Métodos alternativos falharam:`, altError.message);
          }
        }

        if (!audioData) {
          console.warn(`⚠️ [wppConnect] Tentativa ${attempt}: Nenhum método funcionou`);
          continue;
        }

        let audioBuffer: Buffer;
        let mimeType: string = message.mimetype || 'audio/ogg';
        if (typeof audioData === 'string') {
          try {
            if (audioData.length === 0) continue;
            audioBuffer = Buffer.from(audioData, 'base64');
            mimeType = 'audio/ogg';
          } catch (error) {
            console.warn(`⚠️ Tentativa ${attempt}: Erro ao converter string base64:`, error);
            continue;
          }
        } else if (Array.isArray(audioData) || Buffer.isBuffer(audioData)) {
          audioBuffer = Buffer.isBuffer(audioData) ? audioData : Buffer.from(audioData);
          mimeType = 'audio/ogg';
        } else if (audioData && typeof audioData === 'object') {
          if (!audioData.data) {
            console.warn(`⚠️ Tentativa ${attempt}: audioData.data está undefined`);
            continue;
          }
          audioBuffer = Buffer.from(audioData.data, 'base64');
          mimeType = audioData.mimetype || 'audio/ogg';
        } else {
          console.warn(`⚠️ Tentativa ${attempt}: audioData em formato inesperado:`, typeof audioData);
          continue;
        }
        if (audioBuffer.length === 0) {
          console.warn(`⚠️ Tentativa ${attempt}: Buffer vazio`);
          continue;
        }
        return { buffer: audioBuffer, mimeType };
      } catch (error) {
        console.error(`❌ Erro na tentativa ${attempt} de download:`, error);
        if (attempt === maxRetries) throw error;
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }
    return null;
  }

  /**
   * Processar mensagem de texto
   */
  private async processTextMessage(message: any): Promise<void> {
    try {
      console.log('💬 Processando mensagem de texto...');

      const contact = await this.getOrCreateContact(message.from);
      await this.saveMessage(message, contact);

      if (contact.registration_status === 'registered') {
        const iContact = this.convertToIContact(contact);
        const aiResponse = await this.geminiService.generateResponse(iContact, message.body);
        
        await this.sendText(message.from, aiResponse.response);
        console.log('✅ Resposta de texto enviada');
      }

    } catch (error) {
      console.error('❌ Erro ao processar mensagem de texto:', error);
    }
  }

  /**
   * Inicializar handler automático
   */
  private async initializeAutomaticHandler(): Promise<void> {
    if (!this.client) return;
    try {
      this.automaticHandler = new AutomaticWhatsAppHandler(this.client);
      this.automaticHandler.activate();

      audioQueue.on('responseReady', async (data) => {
        try {
          await this.sendText(data.phoneNumber, data.response);
          console.log(`✅ Resposta automática enviada para: ${data.phoneNumber}`);
        } catch (error) {
          console.error('❌ Erro ao enviar resposta automática da fila:', error);
        }
      });

      console.log('🤖 Handler automático inicializado e ouvindo a fila');

    } catch (error) {
      console.error('❌ Erro ao inicializar handler automático:', error);
    }
  }

  /**
   * Verificar status do número usando wa-js
   */
  public async checkNumberStatus(contactId: string): Promise<{
    id: string;
    isBusiness: boolean;
    canReceiveMessage: boolean;
    numberExists: boolean;
    status: number;
  }> {
    try {
      if (!this.client || !this.connectionStatus.isConnected) {
        throw new Error('Cliente não conectado');
      }

      // Remove caracteres não numéricos e prepara o número
      const cleanPhone = contactId.replace(/\D/g, '');

      // Tenta diferentes formatos brasileiros
      const possibleFormats = [
        `55${cleanPhone}@c.us`,           // Com código do país
        `${cleanPhone}@c.us`,             // Sem código do país
        `55${cleanPhone.substring(2)}@c.us` // Remove DDD duplicado se houver
      ];

      for (const format of possibleFormats) {
        try {
          console.log(`🔍 Verificando número: ${format}`);

          const result = await this.client.checkNumberStatus(format);

          if (result && result.numberExists) {
            console.log(`✅ Número válido encontrado: ${result.id._serialized}`);
            return {
              id: result.id._serialized,
              isBusiness: result.isBusiness || false,
              canReceiveMessage: true,
              numberExists: true,
              status: 200
            };
          }
        } catch (error) {
          console.log(`❌ Formato ${format} inválido:`, error.message);
          continue;
        }
      }

      // Se nenhum formato funcionou
      return {
        id: contactId,
        isBusiness: false,
        canReceiveMessage: false,
        numberExists: false,
        status: 404
      };

    } catch (error) {
      console.error('❌ Erro ao verificar status do número:', error);
      return {
        id: contactId,
        isBusiness: false,
        canReceiveMessage: false,
        numberExists: false,
        status: 500
      };
    }
  }

  /**
   * Enviar mensagem de texto
   */
  public async sendText(to: string, message: string): Promise<boolean> {
    try {
      if (!this.client || !this.connectionStatus.isConnected) {
        throw new Error('Cliente não conectado para enviar mensagem');
      }

      console.log(`📞 Validando número: ${to}`);

      // Verificar e validar o número usando wa-js
      const numberStatus = await this.checkNumberStatus(to);

      if (!numberStatus.numberExists || !numberStatus.canReceiveMessage) {
        throw new Error(`Número ${to} não existe ou não pode receber mensagens`);
      }

      console.log(`✅ Número validado: ${to} → ${numberStatus.id}`);

      // Usar o ID corrigido pelo WhatsApp
      await this.client.sendText(numberStatus.id, message);
      console.log(`📤 Mensagem enviada para ${numberStatus.id}`);
      return true;
    } catch (error) {
      console.error(`❌ Erro ao enviar mensagem para ${to}:`, error);
      return false;
    }
  }

  private async sendErrorMessage(to: string, errorMsg: string): Promise<void> {
    try {
      await this.sendText(to, errorMsg);
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de erro:', error);
    }
  }

  /**
   * Handlers de eventos wppConnect
   */
  private handleQRCode(_base64Qr: string, asciiQR: string, attempts: number): void {
    console.log(`\n🔲 ===== QR CODE PARA WHATSAPP (Tentativa: ${attempts}) =====`);
    console.log(asciiQR || 'QR Code gerado - Escaneie com WhatsApp');
    console.log('===================================================\n');
    this.connectionStatus.sessionStatus = 'qr_ready';
  }

  private handleStatusChange(statusSession: string, session: string): void {
    console.log(`📱 Status: ${statusSession} | Sessão: ${session}`);
    this.connectionStatus.sessionStatus = statusSession;
    
    switch (statusSession) {
      case 'isLogged':
        this.connectionStatus.isConnected = true;
        this.connectionStatus.isAuthenticated = true;
        this.reconnectAttempts = 0;
        console.log('✅ WhatsApp conectado e autenticado!');
        this.getDeviceInfo();
        break;
      case 'notLogged':
      case 'browserClose':
      case 'autocloseCalled':
        this.connectionStatus.isConnected = false;
        this.connectionStatus.isAuthenticated = false;
        console.log(`❌ WhatsApp desconectado (Motivo: ${statusSession}). Agendando reconexão...`);
        this.scheduleReconnect();
        break;
      case 'inChat':
        this.connectionStatus.isConnected = true;
        console.log('💬 Em chat - Pronto para receber mensagens!');
        break;
    }
  }

  private handleLoadingScreen(percent: number, message: string): void {
    console.log(`⏳ Carregando: ${percent}% - ${message}`);
  }

  private async getDeviceInfo(): Promise<void> {
    try {
      if (this.client) {
        const info = await this.client.getHostDevice();
        this.connectionStatus.deviceInfo = info;
        this.connectionStatus.phoneNumber = info.id?.user;
        console.log('📱 Dispositivo conectado:', { phone: info.id?.user, name: info.pushname, platform: info.platform });
      }
    } catch (error) {
      console.error('❌ Erro ao obter info do dispositivo:', error);
    }
  }

  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('💀 Máximo de tentativas de reconexão atingido. Verifique o problema manualmente.');
      return;
    }
    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 60000);
    this.reconnectAttempts++;
    console.log(`🔄 Agendando reconexão em ${delay / 1000}s (tentativa ${this.reconnectAttempts})`);
    if (this.reconnectInterval) clearTimeout(this.reconnectInterval);
    this.reconnectInterval = setTimeout(() => this.initialize(), delay);
  }

  /**
   * Métodos auxiliares
   */
  private async getOrCreateContact(whatsappPhone: string) {
    const brazilianPhone = this.normalizePhoneToBrazilian(whatsappPhone);
    let contact = await contactService.findByPhone(brazilianPhone);
    if (!contact) {
      contact = await contactService.create({
        phone: brazilianPhone,
        name: `Contato ${brazilianPhone.slice(-4)}`,
        last_interaction: new Date().toISOString(),
        is_active: true,
        registration_status: 'unregistered',
        evaluation_messages: 0,
        interest_score: 0
      });
    }
    return contact;
  }

  private normalizePhoneToBrazilian(whatsappPhone: string): string {
    let phone = whatsappPhone.replace('@c.us', '');
    if (phone.startsWith('55')) phone = phone.substring(2);
    if (phone.length === 11) {
      const ddd = phone.substring(0, 2);
      const nono = phone.substring(2, 3);
      const primeiros4 = phone.substring(3, 7);
      const ultimos4 = phone.substring(7, 11);
      return `(${ddd}) ${nono}${primeiros4}-${ultimos4}`;
    }
    return phone;
  }

  private convertToIContact(contact: any) {
    return {
      name: contact.name, phone: contact.phone, babyGender: contact.baby_gender || 'unknown',
      isActive: contact.is_active, lastInteraction: new Date(contact.last_interaction),
      registrationStatus: contact.registration_status, evaluationMessages: contact.evaluation_messages,
      interestScore: contact.interest_score,
      evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
      createdAt: new Date(contact.created_at || ''), updatedAt: new Date(contact.updated_at || ''),
      updateInteraction: async () => {}, incrementEvaluationMessages: async () => {}, updateInterestScore: async () => {},
      markAsRegistered: async () => {}, markAsNotInterested: async () => {}, deactivate: async () => {}, reactivate: async () => {}
    };
  }

  /**
   * Salvar mensagem no histórico (nova versão para interface WhatsApp & IA)
   */
  private async saveMessageToHistory(message: any): Promise<void> {
    try {
      console.log('💾 Salvando mensagem no histórico...');

      // Buscar ou criar contato
      const contact = await this.getOrCreateContact(message.from);

      // Determinar conteúdo da mensagem
      let content = message.body || '[MENSAGEM SEM TEXTO]';
      if (message.type === 'ptt' || message.type === 'audio' || message.type === 'voice') {
        content = '🎵 [MENSAGEM DE ÁUDIO]';
      } else if (message.media && !message.body) {
        content = '📎 [MÍDIA]';
      }

      // Salvar no banco via API interna
      const messageData = {
        contact_id: contact.id,
        content,
        type: message.type,
        from_me: false, // Mensagem recebida
        timestamp: new Date(message.timestamp * 1000).toISOString(),
        message_id: message.id,
        status: 'received'
      };

      await messageService.create(messageData);
      console.log(`✅ Mensagem salva no histórico para ${contact.name}`);

    } catch (error) {
      console.error('❌ Erro ao salvar mensagem no histórico:', error);
    }
  }

  private async saveMessage(message: any, contact: any): Promise<void> {
    try {
      await messageService.create({
        contact_id: contact.id, content: message.body || '[MENSAGEM SEM TEXTO]',
        type: message.type, from_me: message.fromMe,
        timestamp: new Date(message.timestamp * 1000).toISOString(), message_id: message.id
      });
    } catch (error) {
      console.error('❌ Erro ao salvar mensagem:', error);
    }
  }

  /**
   * Métodos públicos para controle
   */
  public getConnectionStatus(): ConnectionStatus {
    return { ...this.connectionStatus };
  }

  public getStats() {
    return {
      connection: this.connectionStatus,
      automaticHandler: this.automaticHandler?.getStats() || null,
      audioQueue: audioQueue.getStats(),
      reconnectAttempts: this.reconnectAttempts
    };
  }

  public async disconnect(): Promise<void> {
    try {
      if (this.reconnectInterval) clearTimeout(this.reconnectInterval);
      if (this.keepAliveInterval) clearInterval(this.keepAliveInterval); // ATUALIZADO
      this.reconnectInterval = null;
      this.keepAliveInterval = null; // ATUALIZADO

      this.automaticHandler?.deactivate();

      if (this.client) {
        await this.client.close();
        this.client = null;
      }
      this.connectionStatus = { isConnected: false, isAuthenticated: false, sessionStatus: 'disconnected' };
      console.log('🛑 wppConnect desconectado');
    } catch (error) {
      console.error('❌ Erro ao desconectar:', error);
    }
  }
}

// Instância singleton
export const wppConnectIntegration = new WppConnectIntegration(new GeminiAIService());