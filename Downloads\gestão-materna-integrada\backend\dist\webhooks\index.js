"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupWebhooks = void 0;
const Contact_1 = require("../models/Contact");
const setupWebhooks = (app, whatsappClient, geminiService) => {
    // Webhook para processar mensagens recebidas
    app.post('/webhook/message', async (req, res) => {
        try {
            const { from, body } = req.body;
            console.log('📨 Webhook recebido:', { from, body: body.substring(0, 50) + '...' });
            // Normalizar telefone
            const normalizedPhone = from.replace(/\D/g, '');
            // Buscar contato pelo telefone
            let contact = await Contact_1.Contact.findOne({
                phone: { $regex: normalizedPhone.slice(-10) } // Buscar pelos últimos 10 dígitos
            });
            if (!contact) {
                console.log('👤 Contato não encontrado - será processado pelo WhatsApp client');
                return res.status(200).json({
                    success: true,
                    message: 'Mensagem será processada pelo sistema principal',
                    action: 'forwarded_to_main_system'
                });
            }
            console.log('👤 Contato encontrado:', contact.name, 'Status:', contact.registrationStatus);
            // O processamento será feito pelo WhatsApp client que já tem toda a lógica
            // Este webhook serve apenas para notificação externa
            res.json({
                success: true,
                contact: {
                    id: contact._id,
                    name: contact.name,
                    registrationStatus: contact.registrationStatus
                },
                message: 'Mensagem processada pelo sistema principal'
            });
        }
        catch (error) {
            console.error('❌ Erro no webhook:', error);
            res.status(500).json({ error: 'Erro ao processar mensagem' });
        }
    });
    // Webhook para status de conexão
    app.post('/webhook/status', (req, res) => {
        const { status } = req.body;
        console.log('Status do WhatsApp:', status);
        res.json({ success: true });
    });
};
exports.setupWebhooks = setupWebhooks;
