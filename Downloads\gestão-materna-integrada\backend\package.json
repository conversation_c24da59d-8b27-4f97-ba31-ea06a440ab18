{"name": "gestacao-materna-backend", "version": "1.0.0", "description": "Backend para sistema de gestão materna integrada com WhatsApp e IA", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "build": "tsc", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:webhooks": "ts-node src/scripts/testWebhooks.ts", "test:interest": "ts-node src/scripts/testInterestEvaluation.ts", "test:interest:neutral": "ts-node src/scripts/testInterestEvaluation.ts neutral", "test:interest:analysis": "ts-node src/scripts/testInterestEvaluation.ts analysis", "test:coverage": "NODE_ENV=test jest --coverage", "test:routes": "NODE_ENV=test jest src/test/routes", "test:verbose": "NODE_ENV=test jest --verbose", "seed": "ts-node src/scripts/seed.ts", "seed:templates": "ts-node src/scripts/seedTemplates.ts"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@supabase/supabase-js": "^2.50.0", "@tailwindcss/postcss": "^4.1.8", "@wppconnect-team/wppconnect": "^1.37.2", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "kind-of": "^6.0.3", "multer": "^2.0.1", "qrcode": "^1.5.3", "socket.io": "^4.7.2", "ts-node": "^10.9.2", "whatsapp-web.js": "^1.23.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.13", "@types/node": "^20.10.5", "@types/qrcode": "^1.5.5", "@types/supertest": "^2.0.16", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.3.4", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}