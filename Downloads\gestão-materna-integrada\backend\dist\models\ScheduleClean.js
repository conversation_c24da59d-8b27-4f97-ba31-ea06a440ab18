"use strict";
// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use scheduleService do Supabase em vez deste modelo
Object.defineProperty(exports, "__esModule", { value: true });
exports.Schedule = void 0;
// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use scheduleService do Supabase para todas as operações
// MODELO REMOVIDO - Use scheduleService do Supabase
exports.Schedule = {
    find: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    findById: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    findOne: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    create: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    countDocuments: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    aggregate: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    updateMany: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    deleteMany: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); },
    findByIdAndDelete: () => { throw new Error('MongoDB removido - Use scheduleService do Supabase'); }
};
