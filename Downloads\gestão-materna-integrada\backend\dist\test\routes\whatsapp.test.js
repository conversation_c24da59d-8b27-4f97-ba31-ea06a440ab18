"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
describe('Rotas do WhatsApp', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    let authToken;
    let user;
    beforeEach(async () => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
        // Criar usuário com permissões para WhatsApp
        user = await (0, testHelpers_1.createUserWithPermissions)([
            'read:contacts',
            'write:messages',
            'send:bulk_messages',
            'read:analytics'
        ], 'coordinator');
        authToken = (0, testHelpers_1.generateTestToken)(user._id.toString(), user.role);
    });
    describe('GET /api/whatsapp/status', () => {
        it('deve retornar status do WhatsApp quando conectado', async () => {
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/whatsapp/status')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.connected).toBe(true);
            expect(response.body.status).toBe('connected');
            expect(response.body.authenticated).toBe(true);
            expect(response.body.connectionState).toBe('CONNECTED');
            expect(mockWhatsAppClient.getStatus).toHaveBeenCalledTimes(1);
        });
        it('deve retornar status quando desconectado', async () => {
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: false,
                authenticated: false,
                status: 'disconnected',
                connectionState: 'UNPAIRED',
                qr: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...',
                info: null
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/whatsapp/status')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.connected).toBe(false);
            expect(response.body.status).toBe('disconnected');
            expect(response.body.authenticated).toBe(false);
            expect(response.body.connectionState).toBe('UNPAIRED');
            expect(response.body.qr).toBeDefined();
        });
        it('deve tratar erro do serviço WhatsApp', async () => {
            mockWhatsAppClient.getStatus.mockRejectedValue(new Error('Erro de conexão'));
            const response = await (0, supertest_1.default)(app)
                .get('/api/whatsapp/status')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(500);
            expect(response.body.error).toBe('Erro interno do servidor');
            expect(response.body.code).toBe('INTERNAL_ERROR');
        });
    });
    describe('POST /api/whatsapp/send', () => {
        it('deve enviar mensagem com dados válidos', async () => {
            const messageData = {
                phone: '+5511999999999',
                message: 'Olá! Como você está?'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockResolvedValue(true);
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(200);
            expect(response.body.message).toBe('Mensagem enviada com sucesso');
            expect(response.body.phone).toBe(messageData.phone);
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledWith(messageData.phone, messageData.message);
        });
        it('deve rejeitar mensagem sem destinatário', async () => {
            const messageData = {
                message: 'Olá! Como você está?'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
            expect(mockWhatsAppClient.sendMessage).not.toHaveBeenCalled();
        });
        it('deve rejeitar mensagem sem conteúdo', async () => {
            const messageData = {
                phone: '+5511999999999'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
            expect(mockWhatsAppClient.sendMessage).not.toHaveBeenCalled();
        });
        it('deve tratar erro do serviço WhatsApp', async () => {
            const messageData = {
                phone: '+5511999999999',
                message: 'Olá! Como você está?'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockResolvedValue(false);
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(500);
            expect(response.body.error).toBe('Falha ao enviar mensagem');
            expect(response.body.code).toBe('SEND_FAILED');
        });
        it('deve enviar mensagem com caracteres especiais', async () => {
            const messageData = {
                phone: '+5511999999999',
                message: 'Olá! 😊 Como está sua gravidez? 🤰'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockResolvedValue(true);
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(200);
            expect(response.body.message).toBe('Mensagem enviada com sucesso');
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledWith(messageData.phone, messageData.message);
        });
    });
    describe('POST /api/whatsapp/send-bulk', () => {
        it('deve enviar mensagens em massa', async () => {
            const bulkData = {
                contacts: [
                    { phone: '+5511999999999' },
                    { phone: '+5511888888888' },
                    { phone: '+5511777777777' }
                ],
                message: 'Mensagem em massa para todas as gestantes'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            // Mock das chamadas individuais de sendMessage
            mockWhatsAppClient.sendMessage
                .mockResolvedValueOnce(true) // primeira mensagem
                .mockResolvedValueOnce(true) // segunda mensagem
                .mockResolvedValueOnce(false); // terceira mensagem falha
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(200);
            expect(response.body.message).toBe('Envio em massa concluído');
            expect(response.body.results.total).toBe(3);
            expect(response.body.results.sent).toBe(2);
            expect(response.body.results.failed).toBe(1);
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledTimes(3);
        });
        it('deve rejeitar envio em massa sem contatos', async () => {
            const bulkData = {
                message: 'Mensagem sem destinatários'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
            expect(mockWhatsAppClient.sendMessage).not.toHaveBeenCalled();
        });
        it('deve rejeitar envio em massa sem mensagem', async () => {
            const bulkData = {
                contacts: [{ phone: '+5511999999999' }, { phone: '+5511888888888' }]
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
            expect(mockWhatsAppClient.sendMessage).not.toHaveBeenCalled();
        });
        it('deve tratar erro no envio em massa', async () => {
            const bulkData = {
                contacts: [{ phone: '+5511999999999' }],
                message: 'Mensagem de teste'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockRejectedValue(new Error('Erro no envio em massa'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(200);
            expect(response.body.results.failed).toBe(1);
            expect(response.body.results.details[0].error).toBe('Erro no envio em massa');
        });
        it('deve lidar com lista vazia de contatos', async () => {
            const bulkData = {
                contacts: [],
                message: 'Mensagem para lista vazia'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
        });
    });
    describe('Integração com contatos', () => {
        it('deve enviar mensagem para contato existente', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const messageData = {
                phone: contact.phone,
                message: 'Olá! Como está sua gravidez?'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockResolvedValue(true);
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(messageData)
                .expect(200);
            expect(response.body.message).toBe('Mensagem enviada com sucesso');
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledWith(contact.phone, messageData.message);
        });
        it('deve enviar mensagens em massa para contatos ativos', async () => {
            // Criar contatos de teste
            const contact1 = await (0, testHelpers_1.createTestContact)({ isActive: true });
            const contact2 = await (0, testHelpers_1.createTestContact)({ isActive: true });
            const contact3 = await (0, testHelpers_1.createTestContact)({ isActive: false });
            const bulkData = {
                contacts: [
                    { phone: contact1.phone },
                    { phone: contact2.phone },
                    { phone: contact3.phone }
                ],
                message: 'Mensagem para gestantes ativas'
            };
            // Mock do status conectado
            mockWhatsAppClient.getStatus.mockResolvedValue({
                connected: true,
                authenticated: true,
                status: 'connected',
                connectionState: 'CONNECTED',
                qr: null,
                info: { user: '<EMAIL>' }
            });
            mockWhatsAppClient.sendMessage.mockResolvedValue(true);
            const response = await (0, supertest_1.default)(app)
                .post('/api/whatsapp/send-bulk')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(bulkData)
                .expect(200);
            expect(response.body.message).toBe('Envio em massa concluído');
            expect(mockWhatsAppClient.sendMessage).toHaveBeenCalledTimes(3);
        });
    });
});
