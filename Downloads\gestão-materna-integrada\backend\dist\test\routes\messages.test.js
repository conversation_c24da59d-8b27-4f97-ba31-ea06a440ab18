"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
describe('Rotas de Mensagens', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    beforeEach(() => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
    });
    describe('GET /api/contacts/:id/messages', () => {
        it('deve listar mensagens de um contato específico', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            // Criar mensagens para o contato
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Primeira mensagem',
                type: 'text',
                fromMe: false
            });
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Segunda mensagem',
                type: 'text',
                fromMe: true
            });
            // Criar mensagem de outro contato (não deve aparecer)
            const otherContact = await (0, testHelpers_1.createTestContact)();
            await (0, testHelpers_1.createTestMessage)({
                contact: otherContact._id,
                content: 'Mensagem de outro contato',
                type: 'text',
                fromMe: false
            });
            const response = await (0, supertest_1.default)(app)
                .get(`/api/contacts/${contact._id}/messages`)
                .expect(200);
            expect(response.body).toHaveLength(2);
            expect(response.body[0].content).toBe('Segunda mensagem'); // Mais recente primeiro
            expect(response.body[1].content).toBe('Primeira mensagem');
            // Verificar se todas as mensagens são do contato correto
            response.body.forEach((message) => {
                expect(message.contact._id).toBe(contact._id.toString());
            });
        });
        it('deve retornar array vazio para contato sem mensagens', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const response = await (0, supertest_1.default)(app)
                .get(`/api/contacts/${contact._id}/messages`)
                .expect(200);
            expect(response.body).toEqual([]);
        });
        it('deve retornar erro para contato inexistente', async () => {
            const fakeId = '507f1f77bcf86cd799439011';
            const response = await (0, supertest_1.default)(app)
                .get(`/api/contacts/${fakeId}/messages`)
                .expect(200);
            expect(response.body).toEqual([]);
        });
        it('deve ordenar mensagens por timestamp decrescente', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);
            const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Mensagem mais antiga',
                timestamp: twoHoursAgo
            });
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Mensagem mais recente',
                timestamp: now
            });
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Mensagem do meio',
                timestamp: oneHourAgo
            });
            const response = await (0, supertest_1.default)(app)
                .get(`/api/contacts/${contact._id}/messages`)
                .expect(200);
            expect(response.body).toHaveLength(3);
            expect(response.body[0].content).toBe('Mensagem mais recente');
            expect(response.body[1].content).toBe('Mensagem do meio');
            expect(response.body[2].content).toBe('Mensagem mais antiga');
        });
    });
    describe('POST /api/ai/process', () => {
        it('deve processar mensagem com IA', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const requestData = {
                message: 'Estou me sentindo muito ansiosa sobre o parto',
                contactId: contact._id.toString()
            };
            mockGeminiService.generateResponse.mockResolvedValue({
                message: 'Entendo sua ansiedade. É normal sentir isso. Vamos conversar sobre técnicas de relaxamento.',
                sentiment: 'negative'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/process')
                .send(requestData)
                .expect(200);
            expect(response.body.message).toBeDefined();
            expect(response.body.sentiment).toBe('negative');
            expect(mockGeminiService.generateResponse).toHaveBeenCalledWith(expect.objectContaining({ _id: contact._id }), requestData.message);
        });
        it('deve rejeitar processamento sem contactId', async () => {
            const requestData = {
                message: 'Mensagem sem contato'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/process')
                .send(requestData)
                .expect(400);
            expect(response.body.error).toBe('ID do contato é obrigatório');
            expect(mockGeminiService.generateResponse).not.toHaveBeenCalled();
        });
        it('deve retornar erro para contato inexistente', async () => {
            const fakeId = '507f1f77bcf86cd799439011';
            const requestData = {
                message: 'Mensagem para contato inexistente',
                contactId: fakeId
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/process')
                .send(requestData)
                .expect(404);
            expect(response.body.error).toBe('Contato não encontrado');
            expect(mockGeminiService.generateResponse).not.toHaveBeenCalled();
        });
        it('deve tratar erro do serviço de IA', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const requestData = {
                message: 'Mensagem de teste',
                contactId: contact._id.toString()
            };
            mockGeminiService.generateResponse.mockRejectedValue(new Error('Erro na API do Gemini'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/process')
                .send(requestData)
                .expect(500);
            expect(response.body.error).toBe('Erro na API do Gemini');
        });
    });
    describe('POST /api/ai/follow-up', () => {
        it('deve gerar mensagem de acompanhamento', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const requestData = {
                contactId: contact._id.toString()
            };
            mockGeminiService.generateFollowUpMessage.mockResolvedValue('Olá! Como você está se sentindo hoje? Gostaria de conversar sobre sua gravidez?');
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/follow-up')
                .send(requestData)
                .expect(200);
            expect(response.body.message).toBeDefined();
            expect(typeof response.body.message).toBe('string');
            expect(mockGeminiService.generateFollowUpMessage).toHaveBeenCalledWith(expect.objectContaining({ _id: contact._id }));
        });
        it('deve rejeitar geração sem contactId', async () => {
            const requestData = {};
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/follow-up')
                .send(requestData)
                .expect(400);
            expect(response.body.error).toBe('ID do contato é obrigatório');
            expect(mockGeminiService.generateFollowUpMessage).not.toHaveBeenCalled();
        });
        it('deve retornar erro para contato inexistente', async () => {
            const fakeId = '507f1f77bcf86cd799439011';
            const requestData = {
                contactId: fakeId
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/follow-up')
                .send(requestData)
                .expect(404);
            expect(response.body.error).toBe('Contato não encontrado');
            expect(mockGeminiService.generateFollowUpMessage).not.toHaveBeenCalled();
        });
        it('deve tratar erro do serviço de IA', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const requestData = {
                contactId: contact._id.toString()
            };
            mockGeminiService.generateFollowUpMessage.mockRejectedValue(new Error('Erro na geração de follow-up'));
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/follow-up')
                .send(requestData)
                .expect(500);
            expect(response.body.error).toBe('Erro na geração de follow-up');
        });
    });
    describe('Integração de mensagens e IA', () => {
        it('deve processar mensagem e gerar resposta contextual', async () => {
            const contact = await (0, testHelpers_1.createTestContact)({
                name: 'Ana Silva',
                babyGender: 'female'
            });
            // Criar histórico de mensagens
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Estou na 20ª semana de gravidez',
                type: 'text',
                fromMe: false
            });
            const requestData = {
                message: 'Sinto muitos enjoos pela manhã',
                contactId: contact._id.toString()
            };
            mockGeminiService.generateResponse.mockResolvedValue({
                message: 'Os enjoos matinais são comuns no segundo trimestre. Tente comer algo leve antes de levantar.',
                sentiment: 'neutral'
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/process')
                .send(requestData)
                .expect(200);
            expect(response.body.message).toContain('enjoos matinais');
            expect(response.body.sentiment).toBe('neutral');
        });
        it('deve gerar follow-up baseado no histórico', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            // Criar mensagens recentes
            await (0, testHelpers_1.createTestMessage)({
                contact: contact._id,
                content: 'Estou me sentindo bem',
                type: 'text',
                fromMe: false,
                timestamp: new Date()
            });
            const requestData = {
                contactId: contact._id.toString()
            };
            mockGeminiService.generateFollowUpMessage.mockResolvedValue('Que bom saber que você está se sentindo bem! Como está o desenvolvimento do bebê?');
            const response = await (0, supertest_1.default)(app)
                .post('/api/ai/follow-up')
                .send(requestData)
                .expect(200);
            expect(response.body.message).toContain('desenvolvimento do bebê');
        });
    });
});
