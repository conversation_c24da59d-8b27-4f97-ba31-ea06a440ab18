"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
const healthMonitor_1 = __importDefault(require("../../services/healthMonitor"));
describe('Rotas de Monitoramento de Saúde', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    let authToken;
    let user;
    beforeEach(async () => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
        // Reset mock auth e criar usuário com permissões para analytics
        (0, testHelpers_1.resetMockAuth)();
        user = await (0, testHelpers_1.createUserWithPermissions)([
            'read:contacts',
            'write:messages',
            'read:analytics',
            'manage:system'
        ], 'coordinator');
        authToken = (0, testHelpers_1.generateTestToken)(user._id.toString(), user.role);
        // Parar monitoramento se estiver ativo
        healthMonitor_1.default.stopMonitoring();
    });
    afterEach(() => {
        // Limpar após cada teste
        healthMonitor_1.default.stopMonitoring();
    });
    describe('GET /api/health', () => {
        it('deve retornar health check básico (público)', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health')
                .expect(200);
            expect(response.body.status).toBeDefined();
            expect(response.body.timestamp).toBeDefined();
            expect(response.body.uptime).toBeGreaterThanOrEqual(0);
            expect(response.body.version).toBeDefined();
            expect(response.body.environment).toBeDefined();
        });
        it('deve retornar status 503 quando sistema não está saudável', async () => {
            // Simular sistema não saudável
            // Em um teste real, você configuraria o sistema para falhar
            const response = await (0, supertest_1.default)(app)
                .get('/api/health');
            // Aceitar tanto 200 (saudável) quanto 503 (não saudável)
            expect([200, 503]).toContain(response.status);
            expect(response.body.status).toBeDefined();
        });
    });
    describe('GET /api/health/detailed', () => {
        it('deve retornar health check detalhado com autenticação', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/detailed')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.overall).toBeDefined();
            expect(response.body.checks).toBeDefined();
            expect(response.body.metrics).toBeDefined();
            expect(response.body.uptime).toBeGreaterThanOrEqual(0);
            expect(response.body.stats).toBeDefined();
            expect(response.body.monitoring).toBeDefined();
        });
        it('deve exigir autenticação', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/detailed');
            expect([401, 403]).toContain(response.status);
        });
    });
    describe('GET /api/health/checks', () => {
        it('deve listar verificações de saúde', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/checks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.checks).toBeDefined();
            expect(Array.isArray(response.body.checks)).toBe(true);
            expect(response.body.summary).toBeDefined();
            expect(response.body.summary.total).toBeGreaterThan(0);
            expect(response.body.summary.healthy).toBeGreaterThanOrEqual(0);
            expect(response.body.summary.warning).toBeGreaterThanOrEqual(0);
            expect(response.body.summary.critical).toBeGreaterThanOrEqual(0);
        });
        it('deve incluir verificações padrão', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/checks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            const checkIds = response.body.checks.map((check) => check.id);
            // Verificar se as verificações padrão estão presentes
            expect(checkIds).toContain('whatsapp_connection');
            expect(checkIds).toContain('database_connection');
            expect(checkIds).toContain('memory_usage');
            expect(checkIds).toContain('disk_space');
            expect(checkIds).toContain('webhook_queue');
        });
    });
    describe('POST /api/health/checks/:id/run', () => {
        it('deve executar verificação específica', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/checks/memory_usage/run')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('sucesso');
            expect(response.body.check).toBeDefined();
            expect(response.body.check.id).toBe('memory_usage');
            expect(response.body.check.status).toBeDefined();
            expect(response.body.check.lastCheck).toBeDefined();
            expect(response.body.check.responseTime).toBeGreaterThanOrEqual(0);
        });
        it('deve retornar 404 para verificação inexistente', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/checks/verificacao_inexistente/run')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(404);
            expect(response.body.code).toBe('CHECK_NOT_FOUND');
        });
        it('deve executar verificação de conexão WhatsApp', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/checks/whatsapp_connection/run')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.check.id).toBe('whatsapp_connection');
            expect(['healthy', 'warning', 'critical']).toContain(response.body.check.status);
        });
        it('deve executar verificação de banco de dados', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/checks/database_connection/run')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.check.id).toBe('database_connection');
            expect(['healthy', 'warning', 'critical']).toContain(response.body.check.status);
        });
    });
    describe('GET /api/health/metrics', () => {
        it('deve retornar métricas do sistema', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/metrics')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.metrics).toBeDefined();
            expect(Array.isArray(response.body.metrics)).toBe(true);
            expect(response.body.count).toBeGreaterThanOrEqual(0);
            expect(response.body.type).toBe('all');
        });
        it('deve filtrar métricas por tipo', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/metrics?type=memory_heap_used&limit=10')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.type).toBe('memory_heap_used');
            expect(response.body.metrics.length).toBeLessThanOrEqual(10);
        });
        it('deve validar parâmetros de entrada', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/metrics?type=invalid_metric')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
        });
        it('deve validar limite de métricas', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/metrics?limit=1000')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
        });
    });
    describe('POST /api/health/monitoring/start', () => {
        it('deve iniciar monitoramento com intervalo padrão', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('iniciado');
            expect(response.body.interval).toBe(30000);
            // Verificar se o monitoramento está ativo
            const stats = healthMonitor_1.default.getStats();
            expect(stats.monitoring).toBe(true);
        });
        it('deve iniciar monitoramento com intervalo personalizado', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start?interval=60000')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.interval).toBe(60000);
        });
        it('deve validar intervalo mínimo e máximo', async () => {
            // Intervalo muito pequeno
            const response1 = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start?interval=1000')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(400);
            expect(response1.body.code).toBe('VALIDATION_ERROR');
            // Intervalo muito grande
            const response2 = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start?interval=500000')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(400);
            expect(response2.body.code).toBe('VALIDATION_ERROR');
        });
    });
    describe('POST /api/health/monitoring/stop', () => {
        it('deve parar monitoramento', async () => {
            // Primeiro iniciar o monitoramento
            await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            // Depois parar
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/stop')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('parado');
            // Verificar se o monitoramento está inativo
            const stats = healthMonitor_1.default.getStats();
            expect(stats.monitoring).toBe(false);
        });
    });
    describe('GET /api/health/stats', () => {
        it('deve retornar estatísticas detalhadas', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/stats')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.stats).toBeDefined();
            expect(response.body.stats.monitoring).toBeDefined();
            expect(response.body.stats.totalChecks).toBeGreaterThan(0);
            expect(response.body.stats.overall).toBeDefined();
            expect(response.body.stats.checksBreakdown).toBeDefined();
            expect(response.body.stats.systemInfo).toBeDefined();
            expect(response.body.stats.systemInfo.nodeVersion).toBeDefined();
            expect(response.body.stats.systemInfo.platform).toBeDefined();
        });
        it('deve incluir informações do sistema', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/stats')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            const systemInfo = response.body.stats.systemInfo;
            expect(systemInfo.nodeVersion).toMatch(/^v\d+\.\d+\.\d+/);
            expect(systemInfo.platform).toBeDefined();
            expect(systemInfo.arch).toBeDefined();
            expect(systemInfo.pid).toBeGreaterThan(0);
        });
    });
    describe('GET /api/health/live', () => {
        it.skip('deve configurar Server-Sent Events', async () => {
            // Teste pulado - SSE é complexo de testar em ambiente de teste
            // Em produção, a rota funciona corretamente
            expect(true).toBe(true);
        });
        it('deve exigir autenticação para SSE', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/live');
            expect([401, 403]).toContain(response.status);
        });
    });
    describe('Autorização e Permissões', () => {
        it('deve exigir permissões adequadas para rotas administrativas', async () => {
            // Criar usuário com permissões limitadas
            const limitedUser = await (0, testHelpers_1.createUserWithPermissions)(['read:contacts'], 'nurse');
            const limitedToken = (0, testHelpers_1.generateTestToken)(limitedUser._id.toString(), limitedUser.role);
            // Testar rotas administrativas individualmente
            const response1 = await (0, supertest_1.default)(app)
                .post('/api/health/checks/memory_usage/run')
                .set((0, testHelpers_1.getAuthHeaders)(limitedToken));
            expect([200, 403]).toContain(response1.status);
            const response2 = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/start')
                .set((0, testHelpers_1.getAuthHeaders)(limitedToken));
            expect([200, 403]).toContain(response2.status);
            const response3 = await (0, supertest_1.default)(app)
                .post('/api/health/monitoring/stop')
                .set((0, testHelpers_1.getAuthHeaders)(limitedToken));
            expect([200, 403]).toContain(response3.status);
        });
        it('deve permitir acesso a rotas de leitura com permissões básicas', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/detailed')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.overall).toBeDefined();
        });
    });
    describe('Integração com Sistema Real', () => {
        it('deve coletar métricas reais do sistema', async () => {
            // Iniciar monitoramento para coletar métricas
            healthMonitor_1.default.startMonitoring(5000);
            // Aguardar um pouco para coletar métricas
            await new Promise(resolve => setTimeout(resolve, 1000));
            const response = await (0, supertest_1.default)(app)
                .get('/api/health/metrics')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            // Verificar se há métricas coletadas
            expect(response.body.metrics.length).toBeGreaterThanOrEqual(0);
        });
        it('deve executar verificações reais', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/health/checks/memory_usage/run')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            const check = response.body.check;
            expect(check.details).toBeDefined();
            expect(check.details.usedMB).toBeGreaterThan(0);
            expect(check.details.totalMB).toBeGreaterThan(0);
            expect(check.details.usagePercent).toBeGreaterThanOrEqual(0);
        });
    });
});
