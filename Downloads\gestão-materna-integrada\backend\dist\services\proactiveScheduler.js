"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProactiveSchedulerService = void 0;
const Schedule_1 = require("../models/Schedule");
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
class ProactiveSchedulerService {
    constructor(whatsappClient, geminiService) {
        this.isRunning = false;
        this.whatsappClient = whatsappClient;
        this.geminiService = geminiService;
    }
    // Iniciar o scheduler
    async start() {
        if (this.isRunning) {
            console.log('⚠️  Scheduler já está rodando');
            return;
        }
        this.isRunning = true;
        console.log('🚀 Iniciando Proactive Scheduler...');
        // Executar a cada 5 minutos
        setInterval(async () => {
            await this.processScheduledMessages();
        }, 5 * 60 * 1000);
        // Executar imediatamente
        await this.processScheduledMessages();
        // Criar rotinas automáticas diárias
        setInterval(async () => {
            await this.createDailyRoutines();
        }, 24 * 60 * 60 * 1000); // A cada 24 horas
        console.log('✅ Proactive Scheduler iniciado com sucesso!');
    }
    // Parar o scheduler
    stop() {
        this.isRunning = false;
        console.log('🛑 Proactive Scheduler parado');
    }
    // Processar mensagens agendadas
    async processScheduledMessages() {
        try {
            console.log('🔄 Processando mensagens agendadas...');
            // Buscar mensagens pendentes que devem ser enviadas
            const pendingSchedules = await Schedule_1.Schedule.find({
                status: 'pending',
                scheduledFor: { $lte: new Date() }
            })
                .populate('contact')
                .populate('createdBy', 'name email')
                .limit(50); // Processar no máximo 50 por vez
            console.log(`📋 Encontradas ${pendingSchedules.length} mensagens para processar`);
            for (const schedule of pendingSchedules) {
                await this.processSchedule(schedule);
                // Delay entre mensagens para não sobrecarregar
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
            console.log('✅ Processamento de mensagens agendadas concluído');
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagens agendadas:', error);
        }
    }
    // Processar um agendamento específico
    async processSchedule(schedule) {
        try {
            const contact = schedule.contact;
            // Verificar se deve enviar baseado nas condições
            if (!this.shouldSendToContact(schedule, contact)) {
                console.log(`⏭️  Pulando mensagem para ${contact.name} - condições não atendidas`);
                schedule.status = 'cancelled';
                await schedule.save();
                return;
            }
            // Personalizar mensagem
            const personalizedMessage = this.personalizeMessage(schedule, contact);
            // Enviar via WhatsApp
            const success = await this.whatsappClient.sendMessage(contact.phone, personalizedMessage);
            if (success) {
                await schedule.markAsSent();
                // Salvar no histórico de mensagens
                const message = new Message_1.Message({
                    contact: contact._id,
                    content: personalizedMessage,
                    timestamp: new Date(),
                    type: 'text',
                    fromMe: true,
                    category: this.mapScheduleTypeToCategory(schedule.type),
                    priority: schedule.priority,
                    autoResponse: {
                        triggered: true,
                        responseId: schedule._id.toString(),
                        responseText: personalizedMessage,
                        timestamp: new Date()
                    }
                });
                await message.save();
                // Agendar follow-up se necessário
                if (schedule.requiresResponse) {
                    await this.scheduleFollowUp(schedule, contact);
                }
                // Reagendar se for recorrente
                if (schedule.autoReschedule && schedule.frequency) {
                    await this.rescheduleRecurring(schedule);
                }
                console.log(`✅ Mensagem enviada para ${contact.name}: ${schedule.title}`);
            }
            else {
                schedule.attempts += 1;
                if (schedule.attempts >= (schedule.maxAttempts || 3)) {
                    schedule.status = 'failed';
                }
                await schedule.save();
                console.log(`❌ Falha ao enviar mensagem para ${contact.name}`);
            }
        }
        catch (error) {
            console.error(`❌ Erro ao processar agendamento ${schedule._id}:`, error);
            schedule.status = 'failed';
            await schedule.save();
        }
    }
    // Verificar se deve enviar para o contato
    shouldSendToContact(schedule, contact) {
        // Verificar estágio da gestação
        if (schedule.pregnancyStage && contact.pregnancyStage !== schedule.pregnancyStage) {
            return false;
        }
        // Verificar semanas gestacionais
        const gestationalAge = contact.getGestationalAge();
        if (gestationalAge !== null) {
            if (schedule.gestationalWeekMin && gestationalAge < schedule.gestationalWeekMin) {
                return false;
            }
            if (schedule.gestationalWeekMax && gestationalAge > schedule.gestationalWeekMax) {
                return false;
            }
        }
        // Verificar alto risco
        if (schedule.isHighRisk !== undefined && contact.isHighRisk !== schedule.isHighRisk) {
            return false;
        }
        return true;
    }
    // Personalizar mensagem com dados do contato
    personalizeMessage(schedule, contact) {
        const variables = new Map();
        // Variáveis básicas
        variables.set('name', contact.name);
        variables.set('firstName', contact.name.split(' ')[0]);
        // Variáveis da gestação
        const gestationalAge = contact.getGestationalAge();
        if (gestationalAge !== null) {
            variables.set('week', gestationalAge.toString());
            variables.set('weeks', gestationalAge.toString());
        }
        variables.set('stage', this.getStageDescription(contact.pregnancyStage));
        if (contact.dueDate) {
            const daysUntilDue = Math.ceil((contact.dueDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
            variables.set('daysUntilDue', daysUntilDue.toString());
        }
        // Adicionar variáveis personalizadas do agendamento
        if (schedule.variables) {
            for (const [key, value] of schedule.variables) {
                variables.set(key, value);
            }
        }
        return schedule.personalize();
    }
    // Mapear tipo de agendamento para categoria de mensagem
    mapScheduleTypeToCategory(type) {
        const mapping = {
            'routine_checkup': 'medical',
            'milestone_message': 'routine',
            'educational_content': 'routine',
            'emotional_support': 'emotional',
            'reminder': 'administrative',
            'follow_up': 'medical'
        };
        return mapping[type] || 'routine';
    }
    // Obter descrição do estágio
    getStageDescription(stage) {
        const descriptions = {
            'first_trimester': 'primeiro trimestre',
            'second_trimester': 'segundo trimestre',
            'third_trimester': 'terceiro trimestre',
            'postpartum': 'pós-parto'
        };
        return descriptions[stage || ''] || 'gestação';
    }
    // Agendar follow-up (mais espaçado - 15 dias)
    async scheduleFollowUp(originalSchedule, contact) {
        const followUpDate = new Date();
        followUpDate.setDate(followUpDate.getDate() + 15); // 15 dias depois (em vez de 3)
        const followUpSchedule = new Schedule_1.Schedule({
            contact: contact._id,
            type: 'follow_up',
            scheduledFor: followUpDate,
            title: `Follow-up: ${originalSchedule.title}`,
            message: `Olá {{name}}! Há algumas semanas enviei uma mensagem sobre ${originalSchedule.title.toLowerCase()}. Como você está se sentindo? Tem alguma dúvida ou preocupação que gostaria de compartilhar?`,
            messageType: 'question',
            priority: 'medium',
            requiresResponse: true,
            maxAttempts: 2,
            attempts: 0,
            personalized: true,
            createdBy: originalSchedule.createdBy
        });
        await followUpSchedule.save();
        console.log(`📅 Follow-up agendado para ${contact.name} em ${followUpDate.toLocaleDateString('pt-BR')} (15 dias)`);
    }
    // Reagendar mensagem recorrente
    async rescheduleRecurring(schedule) {
        const intervalDays = {
            'daily': 1,
            'weekly': 7,
            'biweekly': 14,
            'monthly': 30,
            'custom': schedule.customInterval || 7
        };
        const days = intervalDays[schedule.frequency || 'weekly'];
        await schedule.reschedule(days);
        console.log(`🔄 Reagendado para ${schedule.scheduledFor.toLocaleDateString()}: ${schedule.title}`);
    }
    // Criar rotinas diárias automáticas
    async createDailyRoutines() {
        try {
            console.log('🌅 Criando rotinas diárias automáticas...');
            // Buscar gestantes ativas
            const activeContacts = await Contact_1.Contact.find({
                isActive: true,
                pregnancyStage: { $in: ['first_trimester', 'second_trimester', 'third_trimester'] }
            });
            for (const contact of activeContacts) {
                await this.createRoutinesForContact(contact);
            }
            console.log('✅ Rotinas diárias criadas com sucesso');
        }
        catch (error) {
            console.error('❌ Erro ao criar rotinas diárias:', error);
        }
    }
    // Criar rotinas para um contato específico (simplificado)
    async createRoutinesForContact(contact) {
        // Sistema simplificado: apenas check-ups de rotina a cada 15 dias
        await this.createSimpleRoutineCheckup(contact);
        // Mensagens motivacionais ocasionais
        await this.createMotivationalMessage(contact);
    }
    // Criar mensagem de marco
    async createMilestoneMessage(contact, milestone) {
        const schedule = new Schedule_1.Schedule({
            contact: contact._id,
            type: 'milestone_message',
            scheduledFor: new Date(),
            title: `Marco: ${milestone.week} semanas`,
            message: milestone.message,
            messageType: 'motivational',
            priority: 'medium',
            requiresResponse: true,
            personalized: true,
            createdBy: contact.createdBy || contact.assignedTo
        });
        await schedule.save();
        console.log(`🎯 Marco criado para ${contact.name}: ${milestone.week} semanas`);
    }
    // Criar check-up de rotina
    async createRoutineCheckup(contact, gestationalAge) {
        // Check-ups mais frequentes no terceiro trimestre
        const checkupInterval = gestationalAge >= 28 ? 7 : 14; // Semanal vs quinzenal
        // Verificar se já existe um check-up recente
        const recentCheckup = await Schedule_1.Schedule.findOne({
            contact: contact._id,
            type: 'routine_checkup',
            scheduledFor: { $gte: new Date(Date.now() - checkupInterval * 24 * 60 * 60 * 1000) }
        });
        if (recentCheckup)
            return;
        const nextCheckup = new Date();
        nextCheckup.setDate(nextCheckup.getDate() + checkupInterval);
        const schedule = new Schedule_1.Schedule({
            contact: contact._id,
            type: 'routine_checkup',
            scheduledFor: nextCheckup,
            title: 'Check-up de rotina',
            message: 'Olá {{name}}! Como você está se sentindo hoje? Tem algum sintoma novo ou preocupação que gostaria de compartilhar? 💙',
            messageType: 'question',
            priority: 'medium',
            requiresResponse: true,
            autoReschedule: true,
            frequency: gestationalAge >= 28 ? 'weekly' : 'biweekly',
            personalized: true,
            createdBy: contact.createdBy || contact.assignedTo
        });
        await schedule.save();
    }
    // Criar conteúdo educacional
    async createEducationalContent(contact, gestationalAge) {
        const educationalContent = this.getEducationalContentForWeek(gestationalAge);
        if (educationalContent) {
            const schedule = new Schedule_1.Schedule({
                contact: contact._id,
                type: 'educational_content',
                scheduledFor: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 horas depois
                title: educationalContent.title,
                message: educationalContent.message,
                messageType: 'educational',
                priority: 'low',
                personalized: true,
                createdBy: contact.createdBy || contact.assignedTo
            });
            await schedule.save();
        }
    }
    // Obter conteúdo educacional por semana
    getEducationalContentForWeek(week) {
        const content = {
            8: {
                title: 'Desenvolvimento do bebê - 8 semanas',
                message: 'Nesta semana, seu bebê tem cerca de 2cm e já possui dedos das mãos e pés! 👶 O coração bate cerca de 150 vezes por minuto. É normal sentir mais cansaço nesta fase.'
            },
            16: {
                title: 'Exames importantes - 16 semanas',
                message: 'Entre 15-20 semanas é o período ideal para o ultrassom morfológico! 🔍 Este exame verifica o desenvolvimento dos órgãos do bebê. Já agendou o seu?'
            },
            24: {
                title: 'Teste de diabetes gestacional',
                message: 'Entre 24-28 semanas é importante fazer o teste de diabetes gestacional. 🩺 É um exame simples que ajuda a garantir sua saúde e a do bebê.'
            },
            32: {
                title: 'Preparação para o parto',
                message: 'Agora é um bom momento para pensar no plano de parto! 📋 Converse com seu médico sobre suas preferências e tire todas as dúvidas.'
            }
        };
        return content[week] || null;
    }
    // Check-up de rotina simplificado (a cada 15 dias)
    async createSimpleRoutineCheckup(contact) {
        // Verificar se já existe um check-up recente (15 dias)
        const recentCheckup = await Schedule_1.Schedule.findOne({
            contact: contact._id,
            type: 'routine_checkup',
            scheduledFor: { $gte: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) }
        });
        if (recentCheckup)
            return;
        const nextCheckup = new Date();
        nextCheckup.setDate(nextCheckup.getDate() + 15); // 15 dias
        const babyReference = contact.babyGender === 'male' ? 'seu bebê' :
            contact.babyGender === 'female' ? 'sua bebê' : 'seu bebê';
        const schedule = new Schedule_1.Schedule({
            contact: contact._id,
            type: 'routine_checkup',
            scheduledFor: nextCheckup,
            title: 'Check-up de rotina',
            message: `Olá ${contact.name}! Como você e ${babyReference} estão se sentindo? Tem alguma novidade ou preocupação que gostaria de compartilhar? 💙`,
            messageType: 'question',
            priority: 'medium',
            requiresResponse: true,
            autoReschedule: true,
            frequency: 'biweekly',
            personalized: true,
            createdBy: contact.createdBy
        });
        await schedule.save();
        console.log(`📅 Check-up agendado para ${contact.name} em ${nextCheckup.toLocaleDateString('pt-BR')}`);
    }
    // Mensagem motivacional ocasional
    async createMotivationalMessage(contact) {
        // Enviar mensagem motivacional a cada 30 dias
        const recentMotivational = await Schedule_1.Schedule.findOne({
            contact: contact._id,
            type: 'emotional_support',
            scheduledFor: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        });
        if (recentMotivational)
            return;
        const nextMessage = new Date();
        nextMessage.setDate(nextMessage.getDate() + 30); // 30 dias
        const babyReference = contact.babyGender === 'male' ? 'seu menino' :
            contact.babyGender === 'female' ? 'sua menina' : 'seu bebê';
        const motivationalMessages = [
            `${contact.name}, você está fazendo um trabalho incrível! ${babyReference} está crescendo forte e saudável. Continue cuidando bem de vocês dois! 💕`,
            `Olá ${contact.name}! Lembre-se: cada dia da gestação é especial. ${babyReference} já pode sentir seu amor e carinho. Você é uma mãe maravilhosa! 🌟`,
            `${contact.name}, sua jornada como mãe é única e especial. ${babyReference} tem muita sorte de ter você. Continue confiando em si mesma! 💙`,
            `Oi ${contact.name}! Você está criando vida e isso é mágico. ${babyReference} está se desenvolvendo perfeitamente. Parabéns por cuidar tão bem! ✨`
        ];
        const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];
        const schedule = new Schedule_1.Schedule({
            contact: contact._id,
            type: 'emotional_support',
            scheduledFor: nextMessage,
            title: 'Mensagem motivacional',
            message: randomMessage,
            messageType: 'motivational',
            priority: 'low',
            requiresResponse: false,
            personalized: true,
            createdBy: contact.createdBy
        });
        await schedule.save();
        console.log(`💕 Mensagem motivacional agendada para ${contact.name} em ${nextMessage.toLocaleDateString('pt-BR')}`);
    }
}
exports.ProactiveSchedulerService = ProactiveSchedulerService;
exports.default = ProactiveSchedulerService;
