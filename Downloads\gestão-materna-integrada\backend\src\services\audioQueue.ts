import { EventEmitter } from 'events';
import { GeminiAIService } from './gemini';
import { contactService } from './contactService';
import { messageService } from './messageService';
import { aiEventMiddleware } from '../middleware/eventCapture';
import { wppConnectService } from './wppConnectIntegration';
import InterestEvaluatorService from './interestEvaluator';

interface AudioJob {
  id: string;
  phoneNumber: string;
  audioBuffer: Buffer;
  mimeType: string;
  messageId: string;
  timestamp: Date;
  retries: number;
  priority: 'high' | 'normal' | 'low';
  metadata?: {
    isFirstMessage?: boolean;
    contactExists?: boolean;
    messageType?: 'voice' | 'audio';
  };
}

interface ProcessingResult {
  success: boolean;
  response?: string;
  error?: string;
  contactId?: string;
  processingTime?: number;
}

/**
 * Sistema de fila para processamento de áudios em background
 * Garante que mensagens sejam processadas mesmo com alto volume
 */
export class AudioProcessingQueue extends EventEmitter {
  private queue: AudioJob[] = [];
  private processing: Map<string, AudioJob> = new Map();
  private geminiService: GeminiAIService;
  private interestEvaluator: InterestEvaluatorService;
  private isRunning: boolean = false;
  private maxConcurrent: number = 3;
  private maxRetries: number = 3;
  private processingStats = {
    processed: 0,
    failed: 0,
    avgProcessingTime: 0,
    totalProcessingTime: 0
  };

  constructor(geminiService: GeminiAIService) {
    super();
    this.geminiService = geminiService;
    this.interestEvaluator = new InterestEvaluatorService(geminiService);
    this.startProcessing();
    
    // Limpar jobs antigos a cada 30 minutos
    setInterval(() => {
      this.cleanupOldJobs();
    }, 30 * 60 * 1000);

    console.log('🎵 Sistema de fila de áudio inicializado');
  }

  /**
   * Adicionar áudio à fila de processamento
   */
  public async addAudioJob(
    phoneNumber: string,
    audioBuffer: Buffer,
    mimeType: string,
    messageId: string,
    priority: 'high' | 'normal' | 'low' = 'normal'
  ): Promise<string> {
    const jobId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Verificar se contato existe para definir prioridade
    const existingContacts = await contactService.findAll();
    const contactExists = existingContacts.some(contact => contact.phone === phoneNumber);
    
    const job: AudioJob = {
      id: jobId,
      phoneNumber,
      audioBuffer,
      mimeType,
      messageId,
      timestamp: new Date(),
      retries: 0,
      priority: contactExists ? 'high' : priority, // Priorizar contatos conhecidos
      metadata: {
        isFirstMessage: !contactExists,
        contactExists,
        messageType: mimeType.includes('voice') ? 'voice' : 'audio'
      }
    };

    // Inserir na posição correta baseado na prioridade
    this.insertByPriority(job);
    
    console.log(`📥 Áudio adicionado à fila: ${jobId}`, {
      phoneNumber,
      priority: job.priority,
      queueSize: this.queue.length,
      processing: this.processing.size
    });

    this.emit('jobAdded', job);
    return jobId;
  }

  /**
   * Inserir job na fila respeitando prioridade
   */
  private insertByPriority(job: AudioJob): void {
    const priorityOrder = { high: 0, normal: 1, low: 2 };
    
    let insertIndex = this.queue.length;
    for (let i = 0; i < this.queue.length; i++) {
      if (priorityOrder[job.priority] < priorityOrder[this.queue[i].priority]) {
        insertIndex = i;
        break;
      }
    }
    
    this.queue.splice(insertIndex, 0, job);
  }

  /**
   * Iniciar processamento contínuo da fila
   */
  private startProcessing(): void {
    if (this.isRunning) return;
    
    this.isRunning = true;
    console.log('🚀 Processamento de fila iniciado');
    
    // Processar jobs continuamente
    setInterval(async () => {
      await this.processNextJobs();
    }, 1000); // Verificar a cada segundo
  }

  /**
   * Processar próximos jobs da fila
   */
  private async processNextJobs(): Promise<void> {
    if (this.processing.size >= this.maxConcurrent || this.queue.length === 0) {
      return;
    }

    const availableSlots = this.maxConcurrent - this.processing.size;
    const jobsToProcess = this.queue.splice(0, availableSlots);

    for (const job of jobsToProcess) {
      this.processing.set(job.id, job);
      this.processJob(job).catch(error => {
        console.error(`❌ Erro crítico no processamento do job ${job.id}:`, error);
        this.processing.delete(job.id);
      });
    }
  }

  /**
   * Processar um job individual
   */
  private async processJob(job: AudioJob): Promise<void> {
    const startTime = Date.now();
    
    try {
      console.log(`🎵 Processando áudio: ${job.id}`, {
        phoneNumber: job.phoneNumber,
        attempt: job.retries + 1,
        priority: job.priority
      });

      this.emit('jobStarted', job);

      // 1. Buscar ou criar contato
      let contact = await this.findOrCreateContact(job.phoneNumber);
      
      // 2. Salvar mensagem de áudio no banco (apenas na primeira tentativa)
      if (job.retries === 0) {
        await messageService.create({
          contact_id: contact.id,
          content: '[ÁUDIO RECEBIDO VIA WHATSAPP]',
          type: 'audio',
          from_me: false,
          timestamp: new Date().toISOString(),
          message_id: job.messageId
        });
      }

      // 3. Buscar contexto da conversa
      console.log(`📝 Buscando contexto da conversa para ${contact.name}...`);
      let conversationContext = '';
      try {
        const recentMessages = await messageService.findByContactId(contact.id, 5);
        if (recentMessages && recentMessages.length > 0) {
          conversationContext = recentMessages
            .reverse() // Ordem cronológica
            .map(msg => `${msg.from_me ? 'Você' : contact.name}: ${msg.content}`)
            .join('\n');
          console.log(`📚 Contexto encontrado: ${recentMessages.length} mensagens`);
        } else {
          console.log(`📚 Nenhum contexto anterior encontrado`);
        }
      } catch (error) {
        console.warn(`⚠️ Erro ao buscar contexto:`, error);
      }

      // 4. Verificar se é contato não cadastrado e analisar interesse
      let shouldProcessNormally = true;
      let customResponse = null;

      if (contact.registration_status === 'unregistered') {
        console.log(`🔍 Contato não cadastrado - analisando áudio para interesse no projeto...`);

        // Primeiro, transcrever o áudio para análise
        const transcriptionResponse = await this.geminiService.generateResponse(
          contact,
          'Transcreva este áudio em texto simples, sem comentários adicionais.',
          { buffer: job.audioBuffer, mimetype: job.mimeType }
        );

        const transcription = transcriptionResponse.response;
        console.log(`📝 Transcrição do áudio: "${transcription}"`);

        // Analisar se está relacionado ao projeto
        const analysis = await this.interestEvaluator.analyzeIfRelatedToProject(transcription);
        console.log(`📊 Análise do áudio: Relacionado=${analysis.isRelated}, Tipo=${analysis.messageType}, Confiança=${analysis.confidence}%`);

        if (analysis.messageType === 'greeting') {
          // Saudação simples - resposta inteligente
          console.log(`👋 Saudação detectada - gerando resposta inteligente`);
          customResponse = await this.interestEvaluator.generateGreetingResponse(transcription);
          shouldProcessNormally = false;

        } else if (analysis.isRelated && analysis.shouldOfferRegistration) {
          // Relacionado ao projeto - oferecer cadastro
          console.log(`✅ Áudio relacionado ao projeto - oferecendo cadastro`);
          customResponse = await this.interestEvaluator.generateRegistrationOffer(transcription);

          // Marcar como registrada (simplificado)
          await contactService.update(contact.id, { registration_status: 'registered' });
          shouldProcessNormally = false;

        } else if (analysis.isRelated && !analysis.shouldOfferRegistration) {
          // Relacionado mas precisa de mais contexto
          console.log(`🔍 Relacionado mas precisa de mais contexto - fazendo pergunta`);
          customResponse = 'Entendi. Você está grávida ou tem alguma dúvida sobre gestação? Estou aqui para ajudar! 🧡';
          shouldProcessNormally = false;

        } else if (analysis.messageType === 'unrelated') {
          // Não relacionado - marcar como não interessada
          console.log(`🚫 Áudio não relacionado ao projeto - marcando como não interessada`);
          await contactService.update(contact.id, {
            registration_status: 'not_interested',
            is_active: false
          });

          // Não responder - deixar para humano
          console.log(`🤐 Não respondendo - deixando para atendimento humano`);
          this.processing.delete(job.id);
          return;
        }
      }

      let aiResponse;

      if (shouldProcessNormally) {
        // 5. Processar áudio normalmente com Gemini AI
        console.log(`🤖 Enviando áudio para Gemini AI: ${job.id}`);
        const personalizedMessage = `${contact.name} enviou uma mensagem de áudio via WhatsApp. ${conversationContext ? `Contexto da conversa:\n${conversationContext}\n\nResponda de forma personalizada usando o nome dela (${contact.name}) e considerando o contexto da conversa.` : `Esta é a primeira interação. Responda de forma calorosa usando o nome dela: ${contact.name}.`}`;

        aiResponse = await this.geminiService.generateResponse(
          contact,
          personalizedMessage,
          { buffer: job.audioBuffer, mimetype: job.mimeType }
        );
      } else {
        // Usar resposta customizada da análise de interesse
        aiResponse = {
          response: customResponse,
          sentiment: { type: 'neutral' },
          needs: ['Análise de interesse'],
          suggestions: []
        };
      }

      console.log(`✅ Resposta gerada para ${job.id}:`, aiResponse.response);

      // 5. Salvar resposta da IA no banco
      await messageService.create({
        contact_id: contact.id,
        content: aiResponse.response,
        type: 'text',
        from_me: true,
        timestamp: new Date().toISOString(),
        sentiment: this.mapSentiment(aiResponse.sentiment?.type)
      });

      // 6. Emitir evento para notificações
      aiEventMiddleware(contact.id, 'Áudio processado', aiResponse);

      // 7. Atualizar última interação do contato
      await contactService.update(contact.id, {
        last_interaction: new Date().toISOString()
      });

      const processingTime = Date.now() - startTime;
      
      // Atualizar estatísticas
      this.updateStats(processingTime, true);

      const result: ProcessingResult = {
        success: true,
        response: aiResponse.response,
        contactId: contact.id,
        processingTime
      };

      console.log(`✅ Job ${job.id} processado com sucesso em ${processingTime}ms`);
      this.emit('jobCompleted', job, result);

      // Retornar resposta via WhatsApp com simulação de digitação
      this.emit('responseReady', {
        phoneNumber: job.phoneNumber,
        response: aiResponse.response,
        messageId: job.messageId,
        simulateTyping: true
      });

    } catch (error: any) {
      console.error(`❌ Erro ao processar job ${job.id}:`, error);
      
      const processingTime = Date.now() - startTime;
      this.updateStats(processingTime, false);

      // Tentar novamente se não excedeu limite
      if (job.retries < this.maxRetries) {
        job.retries++;
        job.timestamp = new Date();
        
        // Recolocar na fila com prioridade reduzida
        job.priority = job.priority === 'high' ? 'normal' : 'low';
        this.insertByPriority(job);
        
        console.log(`🔄 Job ${job.id} recolocado na fila (tentativa ${job.retries + 1})`);
        this.emit('jobRetried', job);
      } else {
        console.error(`💀 Job ${job.id} falhou definitivamente após ${job.retries} tentativas`);
        
        const result: ProcessingResult = {
          success: false,
          error: error.message || 'Erro desconhecido',
          processingTime
        };
        
        this.emit('jobFailed', job, result);
      }
    } finally {
      this.processing.delete(job.id);
    }
  }

  /**
   * Buscar ou criar contato
   */
  private async findOrCreateContact(phoneNumber: string) {
    try {
      // Extrair apenas o número do telefone (remover @c.us e prefixos)
      let cleanPhone = phoneNumber.replace('@c.us', '');

      // Remover prefixo 55 se presente (código do Brasil)
      if (cleanPhone.startsWith('55') && cleanPhone.length > 11) {
        cleanPhone = cleanPhone.substring(2);
      }

      console.log(`🔍 Buscando contato para: ${phoneNumber} (limpo: ${cleanPhone})`);

      // Buscar por múltiplos formatos de telefone
      const existingContacts = await contactService.findAll();
      const existingContact = existingContacts.find(contact => {
        if (!contact.phone) return false;

        // Limpar telefone do contato também
        let contactPhone = contact.phone.replace(/\D/g, ''); // Remove tudo que não é dígito

        // Remover prefixo 55 se presente
        if (contactPhone.startsWith('55') && contactPhone.length > 11) {
          contactPhone = contactPhone.substring(2);
        }

        // Comparar números limpos
        return contactPhone === cleanPhone ||
               contactPhone === cleanPhone.substring(1) || // Com/sem 9 inicial
               contactPhone.substring(1) === cleanPhone;
      });

      if (existingContact) {
        console.log(`📞 Contato existente encontrado: ${existingContact.name} (ID: ${existingContact.id})`);

        // Se o nome é genérico (Contato + número), tentar encontrar o nome real
        if (existingContact.name.includes('Contato 558488501582') || existingContact.name.includes('Contato 8488501582')) {
          console.log(`🔍 Nome genérico detectado, buscando nome real...`);

          // Buscar gestantes cadastradas para encontrar o nome real
          try {
            const allContacts = await contactService.findAll();
            const realContact = allContacts.find(contact => {
              if (!contact.phone || contact.id === existingContact.id) return false;

              // Limpar telefone para comparação
              let contactPhone = contact.phone.replace(/\D/g, '');
              if (contactPhone.startsWith('55') && contactPhone.length > 11) {
                contactPhone = contactPhone.substring(2);
              }

              // Verificar se é o mesmo número mas com nome real
              return (contactPhone === cleanPhone ||
                     contactPhone === cleanPhone.substring(1) ||
                     contactPhone.substring(1) === cleanPhone) &&
                     !contact.name.includes('Contato') &&
                     contact.name.length > 3;
            });

            if (realContact) {
              console.log(`👤 Nome real encontrado: "${realContact.name}" para o telefone ${cleanPhone}`);
              const updatedContact = await contactService.update(existingContact.id, {
                name: realContact.name,
                email: realContact.email || existingContact.email,
                baby_gender: realContact.baby_gender || existingContact.baby_gender,
                due_date: realContact.due_date || existingContact.due_date,
                registration_status: realContact.registration_status || existingContact.registration_status
              });
              return updatedContact || { ...existingContact, name: realContact.name };
            }
          } catch (searchError) {
            console.warn(`⚠️ Erro ao buscar nome real:`, searchError.message);
          }

          // Tentar obter nome do WhatsApp como fallback
          try {
            if (wppConnectService && wppConnectService.client) {
              console.log(`📱 Tentando buscar nome no WhatsApp como fallback...`);
              const whatsappContact = await wppConnectService.client.getContact(phoneNumber);
              const realName = whatsappContact.name || whatsappContact.pushname || whatsappContact.verifiedName;

              if (realName && !realName.includes('558488501582') && !realName.includes('Contato') && realName.length > 3) {
                console.log(`📱 Nome do WhatsApp encontrado: "${realName}"`);
                const updatedContact = await contactService.update(existingContact.id, { name: realName });
                return updatedContact || { ...existingContact, name: realName };
              }
            }
          } catch (waError) {
            console.warn(`⚠️ WhatsApp não disponível:`, waError.message);
          }
        }

        return existingContact;
      }

      // Criar novo contato
      console.log(`👤 Criando novo contato para: ${cleanPhone}`);

      const newContact = await contactService.create({
        name: `Contato ${cleanPhone}`,
        phone: cleanPhone,
        baby_gender: 'unknown',
        registration_status: 'unregistered',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      console.log(`✅ Novo contato criado: ${newContact.id}`);
      return newContact;

    } catch (error) {
      console.error('❌ Erro ao buscar/criar contato:', error);
      throw error;
    }
  }

  /**
   * Atualizar estatísticas de processamento
   */
  private updateStats(processingTime: number, success: boolean): void {
    if (success) {
      this.processingStats.processed++;
    } else {
      this.processingStats.failed++;
    }
    
    this.processingStats.totalProcessingTime += processingTime;
    this.processingStats.avgProcessingTime = 
      this.processingStats.totalProcessingTime / 
      (this.processingStats.processed + this.processingStats.failed);
  }

  /**
   * Limpar jobs antigos da memória
   */
  private cleanupOldJobs(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    
    // Remover jobs antigos da fila
    const initialSize = this.queue.length;
    this.queue = this.queue.filter(job => job.timestamp > oneHourAgo);
    
    const removed = initialSize - this.queue.length;
    if (removed > 0) {
      console.log(`🧹 Removidos ${removed} jobs antigos da fila`);
    }
  }

  /**
   * Obter estatísticas da fila
   */
  public getStats() {
    return {
      queue: {
        pending: this.queue.length,
        processing: this.processing.size,
        byPriority: {
          high: this.queue.filter(j => j.priority === 'high').length,
          normal: this.queue.filter(j => j.priority === 'normal').length,
          low: this.queue.filter(j => j.priority === 'low').length
        }
      },
      processing: {
        ...this.processingStats,
        successRate: this.processingStats.processed / 
          (this.processingStats.processed + this.processingStats.failed) * 100
      }
    };
  }

  /**
   * Parar processamento (para shutdown graceful)
   */
  public async stop(): Promise<void> {
    this.isRunning = false;
    
    // Aguardar jobs em processamento terminarem
    while (this.processing.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    console.log('🛑 Sistema de fila de áudio parado');
  }

  /**
   * Mapear sentiment para valores válidos do banco
   */
  private mapSentiment(sentimentType?: string): 'positive' | 'negative' | 'neutral' {
    switch (sentimentType?.toLowerCase()) {
      case 'positive':
      case 'happy':
      case 'joy':
      case 'excited':
        return 'positive';
      case 'negative':
      case 'sad':
      case 'angry':
      case 'worried':
      case 'anxious':
        return 'negative';
      case 'neutral':
      case 'unknown':
      case undefined:
      case null:
      default:
        return 'neutral';
    }
  }
}

// Instância singleton
export const audioQueue = new AudioProcessingQueue(new GeminiAIService());
