"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorEventMiddleware = exports.emitSystemShutdown = exports.emitSystemStartup = exports.templateUsageMiddleware = exports.aiEventMiddleware = exports.eventCaptureMiddleware = void 0;
const eventEmitter_1 = require("../services/eventEmitter");
/**
 * Middleware para capturar eventos automaticamente baseado nas rotas
 */
const eventCaptureMiddleware = (req, res, next) => {
    var _a;
    const originalSend = res.send;
    const originalJson = res.json;
    // Capturar informações da requisição
    const requestInfo = {
        method: req.method,
        path: req.path,
        user_id: (_a = req.user) === null || _a === void 0 ? void 0 : _a.id,
        ip: req.ip,
        user_agent: req.get('User-Agent'),
        timestamp: new Date()
    };
    // Override do método send para capturar respostas
    res.send = function (data) {
        captureEvent(req, res, data, requestInfo);
        return originalSend.call(this, data);
    };
    // Override do método json para capturar respostas JSON
    res.json = function (data) {
        captureEvent(req, res, data, requestInfo);
        return originalJson.call(this, data);
    };
    next();
};
exports.eventCaptureMiddleware = eventCaptureMiddleware;
function captureEvent(req, res, responseData, requestInfo) {
    try {
        const { method, path } = req;
        const statusCode = res.statusCode;
        const userId = requestInfo.user_id;
        // Capturar eventos baseado na rota e método
        if (method === 'POST' && statusCode >= 200 && statusCode < 300) {
            // Eventos de criação
            if (path.includes('/contacts')) {
                eventEmitter_1.systemEventEmitter.emitContactCreated(responseData);
            }
            else if (path.includes('/schedules')) {
                eventEmitter_1.systemEventEmitter.emitScheduleCreated(responseData);
            }
            else if (path.includes('/auth/login')) {
                eventEmitter_1.systemEventEmitter.emitUserLogin(userId, requestInfo);
            }
            else if (path.includes('/export')) {
                const exportType = path.split('/').pop() || 'unknown';
                eventEmitter_1.systemEventEmitter.emitDataExport(userId, exportType, (responseData === null || responseData === void 0 ? void 0 : responseData.count) || 0);
            }
            else if (path.includes('/import')) {
                const importType = path.split('/').pop() || 'unknown';
                eventEmitter_1.systemEventEmitter.emitDataImport(userId, importType, (responseData === null || responseData === void 0 ? void 0 : responseData.stats) || {});
            }
        }
        else if (method === 'PUT' && statusCode >= 200 && statusCode < 300) {
            // Eventos de atualização
            if (path.includes('/contacts/')) {
                const contactId = path.split('/').pop();
                eventEmitter_1.systemEventEmitter.emitContactUpdated(contactId || '', req.body);
            }
        }
        else if (method === 'DELETE' && statusCode >= 200 && statusCode < 300) {
            // Eventos de exclusão
            if (path.includes('/contacts/')) {
                const contactId = path.split('/').pop();
                eventEmitter_1.systemEventEmitter.emitContactDeleted(contactId || '');
            }
        }
        // Capturar eventos de logout
        if (path.includes('/auth/logout') && statusCode >= 200 && statusCode < 300) {
            eventEmitter_1.systemEventEmitter.emitUserLogout(userId);
        }
        // Capturar eventos de WhatsApp
        if (path.includes('/whatsapp/send-bulk') && statusCode >= 200 && statusCode < 300) {
            eventEmitter_1.systemEventEmitter.emitBulkMessageCompleted((responseData === null || responseData === void 0 ? void 0 : responseData.stats) || {});
        }
        // Capturar falhas de health check
        if (path.includes('/health') && statusCode >= 400) {
            eventEmitter_1.systemEventEmitter.emitHealthCheckFailed(path, {
                status_code: statusCode,
                error: (responseData === null || responseData === void 0 ? void 0 : responseData.error) || 'Health check failed'
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao capturar evento:', error);
    }
}
/**
 * Middleware específico para capturar eventos de IA
 */
const aiEventMiddleware = (contactId, message, response) => {
    try {
        // Emitir evento de resposta da IA
        eventEmitter_1.systemEventEmitter.emitAIResponseGenerated(contactId, message, response);
        // Se a resposta contém análise de sentimento, emitir evento específico
        if (response.sentiment) {
            eventEmitter_1.systemEventEmitter.emitSentimentAnalyzed(contactId, {
                sentiment: response.sentiment,
                needs: response.needs,
                suggestions: response.suggestions,
                priority: response.priority || 'média',
                medical_attention: response.medical_attention || false
            });
        }
    }
    catch (error) {
        console.error('❌ Erro ao emitir evento de IA:', error);
    }
};
exports.aiEventMiddleware = aiEventMiddleware;
/**
 * Middleware para capturar uso de templates
 */
const templateUsageMiddleware = (templateId, contactId) => {
    try {
        eventEmitter_1.systemEventEmitter.emitTemplateUsed(templateId, contactId);
    }
    catch (error) {
        console.error('❌ Erro ao emitir evento de template:', error);
    }
};
exports.templateUsageMiddleware = templateUsageMiddleware;
/**
 * Função para emitir evento de startup do sistema
 */
const emitSystemStartup = () => {
    try {
        const serverInfo = {
            node_version: process.version,
            platform: process.platform,
            memory: process.memoryUsage(),
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development',
            port: process.env.PORT || 3001
        };
        eventEmitter_1.systemEventEmitter.emitSystemStartup(serverInfo);
    }
    catch (error) {
        console.error('❌ Erro ao emitir evento de startup:', error);
    }
};
exports.emitSystemStartup = emitSystemStartup;
/**
 * Função para emitir evento de shutdown do sistema
 */
const emitSystemShutdown = (reason) => {
    try {
        eventEmitter_1.systemEventEmitter.emitSystemShutdown(reason);
    }
    catch (error) {
        console.error('❌ Erro ao emitir evento de shutdown:', error);
    }
};
exports.emitSystemShutdown = emitSystemShutdown;
/**
 * Middleware para logs de erro detalhados
 */
const errorEventMiddleware = (error, req, res, next) => {
    var _a;
    try {
        // Log detalhado do erro
        console.error('❌ Erro capturado pelo middleware:', {
            error: error.message,
            stack: error.stack,
            path: req.path,
            method: req.method,
            user_id: (_a = req.user) === null || _a === void 0 ? void 0 : _a.id,
            timestamp: new Date().toISOString(),
            request_body: req.body,
            query_params: req.query
        });
        // Emitir evento de erro se for crítico
        if (error.message.includes('database') || error.message.includes('connection')) {
            eventEmitter_1.systemEventEmitter.emitHealthCheckFailed('database_error', {
                error: error.message,
                path: req.path,
                method: req.method
            });
        }
    }
    catch (captureError) {
        console.error('❌ Erro ao capturar evento de erro:', captureError);
    }
    next(error);
};
exports.errorEventMiddleware = errorEventMiddleware;
