"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const webhookService_1 = __importDefault(require("../services/webhookService"));
const router = (0, express_1.Router)();
// GET /api/webhooks - Listar todos os webhooks
router.get('/', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const endpoints = webhookService_1.default.getEndpoints();
        const stats = webhookService_1.default.getStats();
        res.json({
            endpoints,
            stats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao listar webhooks:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/webhooks - Criar novo webhook
router.post('/', auth_1.authenticate, auth_1.requireAdminOrCoordinator, [
    (0, express_validator_1.body)('url')
        .isURL({ protocols: ['http', 'https'] })
        .withMessage('URL deve ser válida (http/https)'),
    (0, express_validator_1.body)('events')
        .isArray({ min: 1 })
        .withMessage('Deve especificar pelo menos um evento'),
    (0, express_validator_1.body)('events.*')
        .isIn([
        'message_sent', 'message_delivered', 'message_read', 'connection_status', 'qr_updated', 'error',
        'contact_created', 'contact_updated', 'contact_deleted', 'schedule_created', 'schedule_executed',
        'ai_response_generated', 'sentiment_analyzed', 'health_check_failed', 'resource_warning',
        'system_startup', 'system_shutdown', 'bulk_message_completed', 'template_used',
        'user_login', 'user_logout', 'data_export', 'data_import', '*'
    ])
        .withMessage('Evento inválido'),
    (0, express_validator_1.body)('secret')
        .optional()
        .isLength({ min: 16 })
        .withMessage('Secret deve ter pelo menos 16 caracteres'),
    (0, express_validator_1.body)('retryAttempts')
        .optional()
        .isInt({ min: 1, max: 10 })
        .withMessage('Tentativas deve ser entre 1 e 10'),
    (0, express_validator_1.body)('active')
        .optional()
        .isBoolean()
        .withMessage('Active deve ser boolean')
], (0, auth_1.auditLog)('CREATE_WEBHOOK'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { url, events, secret, retryAttempts, active } = req.body;
        const webhookId = webhookService_1.default.registerEndpoint({
            url,
            events,
            secret,
            retryAttempts: retryAttempts || 3,
            active: active !== false
        });
        res.status(201).json({
            message: 'Webhook criado com sucesso',
            webhook: {
                id: webhookId,
                url,
                events,
                active: active !== false,
                retryAttempts: retryAttempts || 3
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao criar webhook:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// PUT /api/webhooks/:id - Atualizar webhook
router.put('/:id', auth_1.authenticate, auth_1.requireAdminOrCoordinator, [
    (0, express_validator_1.param)('id').notEmpty().withMessage('ID do webhook é obrigatório'),
    (0, express_validator_1.body)('url')
        .optional()
        .isURL({ protocols: ['http', 'https'] })
        .withMessage('URL deve ser válida (http/https)'),
    (0, express_validator_1.body)('events')
        .optional()
        .isArray({ min: 1 })
        .withMessage('Deve especificar pelo menos um evento'),
    (0, express_validator_1.body)('events.*')
        .optional()
        .isIn(['message_sent', 'message_delivered', 'message_read', 'connection_status', 'qr_updated', 'error', '*'])
        .withMessage('Evento inválido'),
    (0, express_validator_1.body)('secret')
        .optional()
        .isLength({ min: 16 })
        .withMessage('Secret deve ter pelo menos 16 caracteres'),
    (0, express_validator_1.body)('retryAttempts')
        .optional()
        .isInt({ min: 1, max: 10 })
        .withMessage('Tentativas deve ser entre 1 e 10'),
    (0, express_validator_1.body)('active')
        .optional()
        .isBoolean()
        .withMessage('Active deve ser boolean')
], (0, auth_1.auditLog)('UPDATE_WEBHOOK'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { id } = req.params;
        const updates = req.body;
        const success = webhookService_1.default.updateEndpoint(id, updates);
        if (!success) {
            return res.status(404).json({
                error: 'Webhook não encontrado',
                code: 'WEBHOOK_NOT_FOUND'
            });
        }
        res.json({
            message: 'Webhook atualizado com sucesso',
            webhook: webhookService_1.default.getEndpoints().find(w => w.id === id),
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao atualizar webhook:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// DELETE /api/webhooks/:id - Remover webhook
router.delete('/:id', auth_1.authenticate, auth_1.requireAdminOrCoordinator, [
    (0, express_validator_1.param)('id').notEmpty().withMessage('ID do webhook é obrigatório')
], (0, auth_1.auditLog)('DELETE_WEBHOOK'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { id } = req.params;
        const success = webhookService_1.default.unregisterEndpoint(id);
        if (!success) {
            return res.status(404).json({
                error: 'Webhook não encontrado',
                code: 'WEBHOOK_NOT_FOUND'
            });
        }
        res.json({
            message: 'Webhook removido com sucesso',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao remover webhook:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/webhooks/:id/test - Testar webhook
router.post('/:id/test', auth_1.authenticate, auth_1.requireAdminOrCoordinator, [
    (0, express_validator_1.param)('id').notEmpty().withMessage('ID do webhook é obrigatório')
], (0, auth_1.auditLog)('TEST_WEBHOOK'), async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Dados inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { id } = req.params;
        const result = await webhookService_1.default.testEndpoint(id);
        if (!result.success) {
            return res.status(400).json({
                error: 'Teste do webhook falhou',
                code: 'WEBHOOK_TEST_FAILED',
                details: result.message,
                responseTime: result.responseTime
            });
        }
        res.json({
            message: 'Teste do webhook realizado com sucesso',
            result,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao testar webhook:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/webhooks/stats - Estatísticas dos webhooks
router.get('/stats', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const stats = webhookService_1.default.getStats();
        const endpoints = webhookService_1.default.getEndpoints();
        const detailedStats = {
            ...stats,
            endpointsByStatus: {
                active: endpoints.filter(e => e.active).length,
                inactive: endpoints.filter(e => !e.active).length
            },
            endpointsByEvents: endpoints.reduce((acc, endpoint) => {
                endpoint.events.forEach(event => {
                    acc[event] = (acc[event] || 0) + 1;
                });
                return acc;
            }, {}),
            recentErrors: endpoints
                .filter(e => e.lastError)
                .map(e => ({
                id: e.id,
                url: e.url,
                error: e.lastError,
                lastSuccess: e.lastSuccess
            }))
        };
        res.json({
            stats: detailedStats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao obter estatísticas:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/webhooks/queue/clear - Limpar fila de eventos
router.post('/queue/clear', auth_1.authenticate, auth_1.requireAdminOrCoordinator, (0, auth_1.auditLog)('CLEAR_WEBHOOK_QUEUE'), async (req, res) => {
    try {
        const clearedCount = webhookService_1.default.clearQueue();
        res.json({
            message: 'Fila de webhooks limpa com sucesso',
            clearedEvents: clearedCount,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao limpar fila:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/webhooks/processing/pause - Pausar processamento
router.post('/processing/pause', auth_1.authenticate, auth_1.requireAdminOrCoordinator, (0, auth_1.auditLog)('PAUSE_WEBHOOK_PROCESSING'), async (req, res) => {
    try {
        webhookService_1.default.pauseProcessing();
        res.json({
            message: 'Processamento de webhooks pausado',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao pausar processamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/webhooks/processing/resume - Retomar processamento
router.post('/processing/resume', auth_1.authenticate, auth_1.requireAdminOrCoordinator, (0, auth_1.auditLog)('RESUME_WEBHOOK_PROCESSING'), async (req, res) => {
    try {
        webhookService_1.default.resumeProcessing();
        res.json({
            message: 'Processamento de webhooks retomado',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao retomar processamento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
