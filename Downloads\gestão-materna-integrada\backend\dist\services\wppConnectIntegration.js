"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.wppConnectIntegration = exports.WppConnectIntegration = void 0;
const wppconnect = __importStar(require("@wppconnect-team/wppconnect"));
const whatsappAudioHandler_1 = require("./whatsappAudioHandler");
const audioQueue_1 = require("./audioQueue");
const gemini_1 = require("./gemini");
const contactService_1 = require("./contactService");
const messageService_1 = require("./messageService");
/**
 * Integração completa com wppConnect para processamento automático de áudios
 * Gerencia conexão, autenticação e processamento de mensagens
 */
class WppConnectIntegration {
    constructor(geminiService) {
        this.client = null;
        this.automaticHandler = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = null;
        this.isInitializing = false;
        this.keepAliveInterval = null; // Para manter a sessão ativa
        this.geminiService = geminiService;
        this.connectionStatus = {
            isConnected: false,
            isAuthenticated: false,
            sessionStatus: 'disconnected'
        };
    }
    /**
     * Inicializar conexão com WhatsApp
     */
    async initialize() {
        if (this.isInitializing) {
            console.log('⏳ Inicialização já em andamento...');
            return false;
        }
        this.isInitializing = true;
        try {
            console.log('🚀 Iniciando integração wppConnect...');
            const config = {
                session: 'rafaela-audio-session',
                headless: true,
                devtools: false,
                useChrome: false, // MUDANÇA: Usar Chromium padrão em vez de Chrome
                debug: false,
                logQR: true,
                autoClose: 0,
                disableSpins: true,
                disableWelcome: true,
                updatesLog: false,
                puppeteerOptions: {
                    userDataDir: './tokens/rafaela-audio',
                    executablePath: undefined, // Deixar o Puppeteer escolher automaticamente
                    args: [
                        '--no-sandbox',
                        '--disable-setuid-sandbox',
                        '--disable-dev-shm-usage',
                        '--disable-accelerated-2d-canvas',
                        '--no-first-run',
                        '--no-zygote',
                        '--disable-gpu',
                        '--disable-web-security',
                        '--disable-features=VizDisplayCompositor',
                        '--single-process', // ADICIONADO: Força processo único
                        '--no-default-browser-check',
                        '--disable-default-apps',
                        '--disable-extensions',
                        '--disable-plugins',
                        '--disable-translate',
                        '--disable-background-networking',
                        '--disable-sync'
                    ],
                    handleSIGINT: false,
                    handleSIGTERM: false,
                    handleSIGHUP: false
                }
            };
            this.client = await wppconnect.create({
                ...config,
                catchQR: this.handleQRCode.bind(this),
                statusFind: this.handleStatusChange.bind(this),
                onLoadingScreen: this.handleLoadingScreen.bind(this)
            });
            if (this.client) {
                await this.setupEventListeners();
                await this.initializeAutomaticHandler();
                this.startKeepAlive(); // ATUALIZADO: Inicia o keep-alive para estabilidade
                console.log('✅ wppConnect integração inicializada com sucesso!');
                this.isInitializing = false;
                return true;
            }
            throw new Error('Cliente wppConnect não foi criado');
        }
        catch (error) {
            console.error('❌ Erro ao inicializar wppConnect:', error);
            this.connectionStatus.sessionStatus = 'error';
            this.isInitializing = false;
            this.scheduleReconnect();
            return false;
        }
    }
    /**
     * Configurar listeners de eventos
     */
    async setupEventListeners() {
        if (!this.client)
            return;
        console.log('🎧 Configurando listeners de eventos...');
        this.client.onMessage(async (message) => {
            try {
                await this.handleIncomingMessage(message);
            }
            catch (error) {
                console.error('❌ Erro ao processar mensagem:', error);
            }
        });
        this.client.onStateChange((state) => {
            console.log('📱 Estado alterado:', state);
            this.connectionStatus.sessionStatus = state;
            this.connectionStatus.lastActivity = new Date();
        });
        this.client.onAck((ack) => {
            // Opcional: Descomente para logs muito detalhados de ACK
            // console.log('✅ ACK recebido:', ack.id, ack.ack);
        });
        this.client.onPresenceChanged((presence) => {
            // Opcional: Descomente para logs de presença
            // console.log('👤 Presença alterada:', presence.id, presence.isOnline);
        });
        console.log('✅ Listeners configurados com sucesso');
    }
    /**
     * NOVO: Iniciar verificação periódica para manter a sessão ativa
     */
    startKeepAlive() {
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        this.keepAliveInterval = setInterval(async () => {
            if (this.client && this.connectionStatus.isConnected) {
                try {
                    // Chama uma função leve para manter a sessão ativa
                    const battery = await this.client.getBatteryLevel();
                    console.log(`🔋 Keep-alive: Sessão ativa. Bateria do celular: ${battery}%`);
                }
                catch (error) {
                    console.warn('⚠️ Erro no keep-alive (pode indicar desconexão, tentando reconectar):', error);
                    this.scheduleReconnect();
                }
            }
        }, 4 * 60 * 1000); // A cada 4 minutos
    }
    /**
     * Processar mensagem recebida
     */
    async handleIncomingMessage(message) {
        try {
            if (message.fromMe) {
                return;
            }
            this.connectionStatus.lastActivity = new Date();
            console.log('📨 Mensagem recebida:', {
                from: message.from,
                type: message.type,
                id: message.id,
                timestamp: new Date(message.timestamp * 1000).toISOString(),
                hasBody: !!message.body,
                hasMedia: !!message.media
            });
            // PRIORIDADE PARA ÁUDIO - Verificar múltiplos tipos
            if (message.type === 'ptt' || message.type === 'audio' || message.type === 'voice') {
                console.log('🎵 ÁUDIO DETECTADO! Processando...');
                await this.processAudioMessage(message);
            }
            else if (message.type === 'chat' && message.body) {
                console.log('💬 TEXTO DETECTADO! Processando...');
                await this.processTextMessage(message);
            }
            else {
                console.log(`📝 Tipo de mensagem não processado: ${message.type}`, {
                    hasBody: !!message.body,
                    hasMedia: !!message.media,
                    mimetype: message.mimetype
                });
            }
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagem recebida:', error);
        }
    }
    /**
     * Processar mensagem de áudio
     */
    async processAudioMessage(message) {
        try {
            console.log('🎵 INICIANDO PROCESSAMENTO DE ÁUDIO...');
            console.log('📋 Detalhes da mensagem de áudio:', {
                type: message.type,
                id: message.id,
                from: message.from,
                hasMedia: !!message.media,
                mimetype: message.mimetype
            });
            // Tentar primeiro com handler automático
            if (this.automaticHandler) {
                console.log('🤖 Tentando com handler automático...');
                try {
                    const processed = await this.automaticHandler.processAudioMessage(message);
                    if (processed) {
                        console.log('✅ Áudio enfileirado pelo handler automático');
                        return;
                    }
                }
                catch (handlerError) {
                    console.warn('⚠️ Erro no handler automático:', handlerError);
                }
            }
            // Fallback: processamento direto
            console.warn('🔄 Usando processamento direto de áudio...');
            await this.processAudioDirect(message);
        }
        catch (error) {
            console.error('❌ ERRO CRÍTICO no processamento de áudio:', error);
            await this.sendErrorMessage(message.from, '🎵 Desculpe, tive problemas para processar seu áudio. Tente enviar novamente ou digite sua mensagem.');
        }
    }
    /**
     * Processar áudio diretamente (fallback)
     */
    async processAudioDirect(message) {
        try {
            const audioResult = await this.downloadAudioWithRetry(message);
            if (!audioResult) {
                throw new Error('Não foi possível baixar o áudio após múltiplas tentativas');
            }
            const { buffer: audioBuffer, mimeType } = audioResult;
            console.log('📥 Áudio baixado (fallback):', {
                size: audioBuffer.length,
                mimeType,
                messageId: message.id
            });
            const jobId = await audioQueue_1.audioQueue.addAudioJob(message.from, audioBuffer, mimeType, message.id, 'high');
            console.log(`📋 Áudio adicionado à fila via fallback: ${jobId}`);
            await this.sendText(message.from, '🎵 Recebi seu áudio! Estou processando...');
        }
        catch (error) {
            console.error('❌ Erro no processamento direto de áudio:', error);
            throw error;
        }
    }
    /**
     * Baixar áudio com múltiplas tentativas (método permanece o mesmo)
     */
    async downloadAudioWithRetry(message, maxRetries = 3) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`📥 Tentativa ${attempt}/${maxRetries} de download do áudio...`);
                const audioData = await this.client.downloadMedia(message);
                if (!audioData) {
                    console.warn(`⚠️ Tentativa ${attempt}: downloadMedia retornou null`);
                    continue;
                }
                let audioBuffer;
                let mimeType;
                if (typeof audioData === 'string') {
                    try {
                        if (audioData.length === 0)
                            continue;
                        audioBuffer = Buffer.from(audioData, 'base64');
                        mimeType = 'audio/ogg';
                    }
                    catch (error) {
                        console.warn(`⚠️ Tentativa ${attempt}: Erro ao converter string base64:`, error);
                        continue;
                    }
                }
                else if (Array.isArray(audioData) || Buffer.isBuffer(audioData)) {
                    audioBuffer = Buffer.isBuffer(audioData) ? audioData : Buffer.from(audioData);
                    mimeType = 'audio/ogg';
                }
                else if (audioData && typeof audioData === 'object') {
                    if (!audioData.data) {
                        console.warn(`⚠️ Tentativa ${attempt}: audioData.data está undefined`);
                        continue;
                    }
                    audioBuffer = Buffer.from(audioData.data, 'base64');
                    mimeType = audioData.mimetype || 'audio/ogg';
                }
                else {
                    console.warn(`⚠️ Tentativa ${attempt}: audioData em formato inesperado:`, typeof audioData);
                    continue;
                }
                if (audioBuffer.length === 0) {
                    console.warn(`⚠️ Tentativa ${attempt}: Buffer vazio`);
                    continue;
                }
                return { buffer: audioBuffer, mimeType };
            }
            catch (error) {
                console.error(`❌ Erro na tentativa ${attempt} de download:`, error);
                if (attempt === maxRetries)
                    throw error;
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
        return null;
    }
    /**
     * Processar mensagem de texto
     */
    async processTextMessage(message) {
        try {
            console.log('💬 Processando mensagem de texto...');
            const contact = await this.getOrCreateContact(message.from);
            await this.saveMessage(message, contact);
            if (contact.registration_status === 'registered') {
                const iContact = this.convertToIContact(contact);
                const aiResponse = await this.geminiService.generateResponse(iContact, message.body);
                await this.sendText(message.from, aiResponse.response);
                console.log('✅ Resposta de texto enviada');
            }
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagem de texto:', error);
        }
    }
    /**
     * Inicializar handler automático
     */
    async initializeAutomaticHandler() {
        if (!this.client)
            return;
        try {
            this.automaticHandler = new whatsappAudioHandler_1.AutomaticWhatsAppHandler(this.client);
            this.automaticHandler.activate();
            audioQueue_1.audioQueue.on('responseReady', async (data) => {
                try {
                    await this.sendText(data.phoneNumber, data.response);
                    console.log(`✅ Resposta automática enviada para: ${data.phoneNumber}`);
                }
                catch (error) {
                    console.error('❌ Erro ao enviar resposta automática da fila:', error);
                }
            });
            console.log('🤖 Handler automático inicializado e ouvindo a fila');
        }
        catch (error) {
            console.error('❌ Erro ao inicializar handler automático:', error);
        }
    }
    /**
     * Converter telefone brasileiro para formato WhatsApp
     */
    formatPhoneForWhatsApp(phone) {
        // Remove todos os caracteres não numéricos
        const cleanPhone = phone.replace(/\D/g, '');
        // Se já tem código do país (55), usa como está
        if (cleanPhone.startsWith('55') && cleanPhone.length === 13) {
            return `${cleanPhone}@c.us`;
        }
        // Se tem 11 dígitos (DDD + 9 + número), adiciona código do país
        if (cleanPhone.length === 11) {
            return `55${cleanPhone}@c.us`;
        }
        // Se tem 10 dígitos (DDD + número sem 9), adiciona 55 e 9
        if (cleanPhone.length === 10) {
            const ddd = cleanPhone.substring(0, 2);
            const numero = cleanPhone.substring(2);
            return `55${ddd}9${numero}@c.us`;
        }
        // Fallback: assume que é um número válido e adiciona @c.us
        return `${cleanPhone}@c.us`;
    }
    /**
     * Enviar mensagem de texto
     */
    async sendText(to, message) {
        try {
            if (!this.client || !this.connectionStatus.isConnected) {
                throw new Error('Cliente não conectado para enviar mensagem');
            }
            // Converter formato do telefone
            const whatsappPhone = this.formatPhoneForWhatsApp(to);
            console.log(`📞 Telefone convertido: ${to} → ${whatsappPhone}`);
            await this.client.sendText(whatsappPhone, message);
            console.log(`📤 Mensagem enviada para ${whatsappPhone}`);
            return true;
        }
        catch (error) {
            console.error(`❌ Erro ao enviar mensagem para ${to}:`, error);
            return false;
        }
    }
    async sendErrorMessage(to, errorMsg) {
        try {
            await this.sendText(to, errorMsg);
        }
        catch (error) {
            console.error('❌ Erro ao enviar mensagem de erro:', error);
        }
    }
    /**
     * Handlers de eventos wppConnect
     */
    handleQRCode(_base64Qr, asciiQR, attempts) {
        console.log(`\n🔲 ===== QR CODE PARA WHATSAPP (Tentativa: ${attempts}) =====`);
        console.log(asciiQR || 'QR Code gerado - Escaneie com WhatsApp');
        console.log('===================================================\n');
        this.connectionStatus.sessionStatus = 'qr_ready';
    }
    handleStatusChange(statusSession, session) {
        console.log(`📱 Status: ${statusSession} | Sessão: ${session}`);
        this.connectionStatus.sessionStatus = statusSession;
        switch (statusSession) {
            case 'isLogged':
                this.connectionStatus.isConnected = true;
                this.connectionStatus.isAuthenticated = true;
                this.reconnectAttempts = 0;
                console.log('✅ WhatsApp conectado e autenticado!');
                this.getDeviceInfo();
                break;
            case 'notLogged':
            case 'browserClose':
            case 'autocloseCalled':
                this.connectionStatus.isConnected = false;
                this.connectionStatus.isAuthenticated = false;
                console.log(`❌ WhatsApp desconectado (Motivo: ${statusSession}). Agendando reconexão...`);
                this.scheduleReconnect();
                break;
            case 'inChat':
                this.connectionStatus.isConnected = true;
                console.log('💬 Em chat - Pronto para receber mensagens!');
                break;
        }
    }
    handleLoadingScreen(percent, message) {
        console.log(`⏳ Carregando: ${percent}% - ${message}`);
    }
    async getDeviceInfo() {
        var _a, _b;
        try {
            if (this.client) {
                const info = await this.client.getHostDevice();
                this.connectionStatus.deviceInfo = info;
                this.connectionStatus.phoneNumber = (_a = info.id) === null || _a === void 0 ? void 0 : _a.user;
                console.log('📱 Dispositivo conectado:', { phone: (_b = info.id) === null || _b === void 0 ? void 0 : _b.user, name: info.pushname, platform: info.platform });
            }
        }
        catch (error) {
            console.error('❌ Erro ao obter info do dispositivo:', error);
        }
    }
    scheduleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('💀 Máximo de tentativas de reconexão atingido. Verifique o problema manualmente.');
            return;
        }
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 60000);
        this.reconnectAttempts++;
        console.log(`🔄 Agendando reconexão em ${delay / 1000}s (tentativa ${this.reconnectAttempts})`);
        if (this.reconnectInterval)
            clearTimeout(this.reconnectInterval);
        this.reconnectInterval = setTimeout(() => this.initialize(), delay);
    }
    /**
     * Métodos auxiliares
     */
    async getOrCreateContact(whatsappPhone) {
        const brazilianPhone = this.normalizePhoneToBrazilian(whatsappPhone);
        let contact = await contactService_1.contactService.findByPhone(brazilianPhone);
        if (!contact) {
            contact = await contactService_1.contactService.create({
                phone: brazilianPhone,
                name: `Contato ${brazilianPhone.slice(-4)}`,
                last_interaction: new Date().toISOString(),
                is_active: true,
                registration_status: 'unregistered',
                evaluation_messages: 0,
                interest_score: 0
            });
        }
        return contact;
    }
    normalizePhoneToBrazilian(whatsappPhone) {
        let phone = whatsappPhone.replace('@c.us', '');
        if (phone.startsWith('55'))
            phone = phone.substring(2);
        if (phone.length === 11) {
            const ddd = phone.substring(0, 2);
            const nono = phone.substring(2, 3);
            const primeiros4 = phone.substring(3, 7);
            const ultimos4 = phone.substring(7, 11);
            return `(${ddd}) ${nono}${primeiros4}-${ultimos4}`;
        }
        return phone;
    }
    convertToIContact(contact) {
        return {
            name: contact.name, phone: contact.phone, babyGender: contact.baby_gender || 'unknown',
            isActive: contact.is_active, lastInteraction: new Date(contact.last_interaction),
            registrationStatus: contact.registration_status, evaluationMessages: contact.evaluation_messages,
            interestScore: contact.interest_score,
            evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
            createdAt: new Date(contact.created_at || ''), updatedAt: new Date(contact.updated_at || ''),
            updateInteraction: async () => { }, incrementEvaluationMessages: async () => { }, updateInterestScore: async () => { },
            markAsRegistered: async () => { }, markAsNotInterested: async () => { }, deactivate: async () => { }, reactivate: async () => { }
        };
    }
    async saveMessage(message, contact) {
        try {
            await messageService_1.messageService.create({
                contact_id: contact.id, content: message.body || '[MENSAGEM SEM TEXTO]',
                type: message.type, from_me: message.fromMe,
                timestamp: new Date(message.timestamp * 1000).toISOString(), message_id: message.id
            });
        }
        catch (error) {
            console.error('❌ Erro ao salvar mensagem:', error);
        }
    }
    /**
     * Métodos públicos para controle
     */
    getConnectionStatus() {
        return { ...this.connectionStatus };
    }
    getStats() {
        var _a;
        return {
            connection: this.connectionStatus,
            automaticHandler: ((_a = this.automaticHandler) === null || _a === void 0 ? void 0 : _a.getStats()) || null,
            audioQueue: audioQueue_1.audioQueue.getStats(),
            reconnectAttempts: this.reconnectAttempts
        };
    }
    async disconnect() {
        var _a;
        try {
            if (this.reconnectInterval)
                clearTimeout(this.reconnectInterval);
            if (this.keepAliveInterval)
                clearInterval(this.keepAliveInterval); // ATUALIZADO
            this.reconnectInterval = null;
            this.keepAliveInterval = null; // ATUALIZADO
            (_a = this.automaticHandler) === null || _a === void 0 ? void 0 : _a.deactivate();
            if (this.client) {
                await this.client.close();
                this.client = null;
            }
            this.connectionStatus = { isConnected: false, isAuthenticated: false, sessionStatus: 'disconnected' };
            console.log('🛑 wppConnect desconectado');
        }
        catch (error) {
            console.error('❌ Erro ao desconectar:', error);
        }
    }
}
exports.WppConnectIntegration = WppConnectIntegration;
// Instância singleton
exports.wppConnectIntegration = new WppConnectIntegration(new gemini_1.GeminiAIService());
