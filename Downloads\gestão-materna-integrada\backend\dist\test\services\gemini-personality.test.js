"use strict";
// Teste simplificado da personalização Rafaela
describe('Personalização Rafaela - Configuração', () => {
    describe('Configuração da Personalização', () => {
        it('deve verificar configuração da API', () => {
            const hasApiKey = !!(process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY);
            console.log('🔑 API Key configurada:', hasApiKey ? 'Sim' : 'Não');
            if (hasApiKey) {
                console.log('✅ Personalização da Rafaela está disponível');
            }
            else {
                console.log('⚠️ Para testar a personalização, configure GEMINI_API_KEY');
            }
            expect(true).toBe(true);
        });
    });
    describe('Demonstração da Personalização', () => {
        it('deve mostrar exemplos da personalidade Rafaela', () => {
            console.log('\n🧡 ===== PERSONALIZAÇÃO DA RAFAELA =====');
            console.log('📝 Características implementadas:');
            console.log('   • Tom maternal: "minha querida", "nossa gente"');
            console.log('   • Linguagem de força: "seguimos firmes juntas"');
            console.log('   • Elementos de fé: "Deus abençoa", "com esperança"');
            console.log('   • Inclusão: "juntas somos mais fortes"');
            console.log('   • Emoji principal: 🧡');
            console.log('   • Referência ao nome da gestante');
            console.log('   • Menção ao gênero do bebê quando conhecido');
            console.log('   • Respostas concisas (15-60 palavras)');
            console.log('=====================================\n');
            expect(true).toBe(true);
        });
        it('deve mostrar exemplos de frases características', () => {
            const frasesRafaela = [
                'Seguimos firmes juntas, minha querida',
                'Nossa gente merece o melhor cuidado',
                'Com fé e amor, tudo é possível',
                'Você não está sozinha nessa caminhada',
                'Juntas somos mais fortes',
                'Deus abençoa você e sua filha',
                'Nossa família está aqui para apoiar'
            ];
            console.log('\n🧡 ===== FRASES CARACTERÍSTICAS =====');
            frasesRafaela.forEach((frase, index) => {
                console.log(`   ${index + 1}. "${frase}"`);
            });
            console.log('=====================================\n');
            expect(frasesRafaela.length).toBeGreaterThan(0);
        });
    });
});
