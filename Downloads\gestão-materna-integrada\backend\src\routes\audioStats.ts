import { Router } from 'express';
import { audioQueue } from '../services/audioQueue';

const router = Router();

/**
 * GET /api/audio/stats
 * Obter estatísticas do sistema de processamento de áudio
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = audioQueue.getStats();
    
    res.json({
      success: true,
      data: {
        ...stats,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage()
      }
    });
  } catch (error: any) {
    console.error('❌ Erro ao obter estatísticas de áudio:', error);
    res.status(500).json({
      success: false,
      error: 'Erro interno do servidor',
      message: error.message
    });
  }
});

/**
 * GET /api/audio/health
 * Verificar saúde do sistema de áudio
 */
router.get('/health', async (req, res) => {
  try {
    const stats = audioQueue.getStats();
    const isHealthy = stats.processing.successRate > 80; // 80% de sucesso
    
    res.status(isHealthy ? 200 : 503).json({
      success: true,
      healthy: isHealthy,
      data: {
        successRate: stats.processing.successRate,
        queueSize: stats.queue.pending,
        processing: stats.queue.processing,
        avgProcessingTime: stats.processing.avgProcessingTime,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    console.error('❌ Erro ao verificar saúde do áudio:', error);
    res.status(500).json({
      success: false,
      healthy: false,
      error: error.message
    });
  }
});

/**
 * POST /api/audio/test
 * Testar sistema de processamento de áudio (desenvolvimento)
 */
router.post('/test', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        error: 'Endpoint de teste não disponível em produção'
      });
    }

    const { phoneNumber = '+5511999999999', priority = 'normal' } = req.body;
    
    // Criar áudio de teste (buffer vazio)
    const testAudioBuffer = Buffer.from('test audio data');
    const testMimeType = 'audio/ogg';
    const testMessageId = `test_${Date.now()}`;

    // Adicionar à fila
    const jobId = await audioQueue.addAudioJob(
      phoneNumber,
      testAudioBuffer,
      testMimeType,
      testMessageId,
      priority
    );

    res.json({
      success: true,
      message: 'Job de teste adicionado à fila',
      data: {
        jobId,
        phoneNumber,
        priority,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error: any) {
    console.error('❌ Erro ao criar job de teste:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

export default router;
