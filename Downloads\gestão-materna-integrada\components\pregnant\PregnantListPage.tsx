import React, { useState, useEffect, useCallback, useRef } from 'react'; // Added useRef to import
import PageTitle from '../shared/PageTitle';
import Button from '../shared/Button';
import PregnantTable from './PregnantTable';
import PregnantFormModal from './PregnantFormModal';
import DeleteConfirmModal from './DeleteConfirmModal';
import { AudioChatModal } from '../audio/AudioChatModal';
import { AdvancedAudioChatModal } from '../audio/AdvancedAudioChatModal';
import { PregnantWoman } from '../../types';
import { ICONS } from '../../constants';
import { normalizeBrazilianPhoneNumber } from '../../utils/phoneUtils';
import Input from '../shared/Input';
import { parsePregnantWomenCSV } from '../../utils/csvParser';
import Spinner from '../shared/Spinner';
import { apiService, type Contact } from '../../src/services/apiService';
import { toast } from 'react-toastify';

// Removido: Funções mock - agora usando apenas dados reais do Supabase


const PregnantListPage: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isAudioChatModalOpen, setIsAudioChatModalOpen] = useState(false);
  const [isAdvancedAudioModalOpen, setIsAdvancedAudioModalOpen] = useState(false);
  const [selectedContact, setSelectedContact] = useState<Contact | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [processing, setProcessing] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null); // Changed React.useRef to useRef

  useEffect(() => {
    loadContacts();
  }, []);

  const loadContacts = async () => {
    try {
      setLoading(true);
      console.log('🔍 Carregando gestantes...');
      const data = await apiService.getContacts();
      console.log('📊 Dados recebidos da API:', data);
      console.log('📊 Tipo dos dados:', typeof data);
      console.log('📊 É array?', Array.isArray(data));

      // Garantir que data seja sempre um array
      const safeData = Array.isArray(data) ? data : [];
      console.log('📊 Dados seguros:', safeData);
      console.log('📊 Quantidade de gestantes:', safeData.length);

      setContacts(safeData);
      setFilteredContacts(safeData);

      console.log('✅ Gestantes carregadas com sucesso');
    } catch (error) {
      toast.error('Erro ao carregar contatos');
      console.error('❌ Erro ao carregar contatos:', error);
      // Em caso de erro, definir arrays vazios
      setContacts([]);
      setFilteredContacts([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const lowerSearchTerm = searchTerm.toLowerCase();
    const filtered = contacts.filter(
      (contact) =>
        contact.name.toLowerCase().includes(lowerSearchTerm) ||
        contact.phone.includes(lowerSearchTerm) ||
        (contact.email && contact.email.toLowerCase().includes(lowerSearchTerm))
    );
    setFilteredContacts(filtered);
  }, [searchTerm, contacts]);

  const handleOpenFormModal = (contact?: Contact) => {
    setSelectedContact(contact || null);
    setIsFormModalOpen(true);
  };

  const handleCloseFormModal = () => {
    setSelectedContact(null);
    setIsFormModalOpen(false);
  };

  const handleOpenDeleteModal = (contact: Contact) => {
    setSelectedContact(contact);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setSelectedContact(null);
    setIsDeleteModalOpen(false);
  };

  const handleOpenAudioChatModal = (contact: Contact) => {
    setSelectedContact(contact);
    setIsAudioChatModalOpen(true);
  };

  const handleCloseAudioChatModal = () => {
    setSelectedContact(null);
    setIsAudioChatModalOpen(false);
  };

  const handleOpenAdvancedAudioModal = (contact: Contact) => {
    setSelectedContact(contact);
    setIsAdvancedAudioModalOpen(true);
  };

  const handleCloseAdvancedAudioModal = () => {
    setIsAdvancedAudioModalOpen(false);
    setSelectedContact(null);
  };

  const handleSaveContact = async (contactData: Partial<Contact>) => {
    try {
      if (selectedContact) {
        // Atualizar contato existente - usar id ou _id
        const contactId = selectedContact.id || selectedContact._id;
        if (!contactId) {
          throw new Error('ID do contato não encontrado');
        }
        await apiService.updateContact(contactId, contactData);
        toast.success('Contato atualizado com sucesso!');
      } else {
        // Criar novo contato
        await apiService.createContact(contactData);
        toast.success('Contato criado com sucesso!');
      }
      handleCloseFormModal();
      loadContacts();
    } catch (error) {
      toast.error('Erro ao salvar contato');
      console.error('Erro ao salvar contato:', error);
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact) return;

    try {
      // Corrigido: usar 'id' em vez de '_id' para Supabase
      await apiService.deleteContact(selectedContact.id);
      toast.success('Contato excluído com sucesso!');
      handleCloseDeleteModal();
      loadContacts();
    } catch (error) {
      toast.error('Erro ao excluir contato');
      console.error('Erro ao excluir contato:', error);
    }
  };

  const handleSendMessage = async (contact: Contact, message: string) => {
    try {
      await apiService.sendMessage(contact.phone, message);
      toast.success('Mensagem enviada com sucesso!');
    } catch (error) {
      toast.error('Erro ao enviar mensagem');
      console.error('Erro ao enviar mensagem:', error);
    }
  };

  const handleGenerateFollowUp = async (contact: Contact) => {
    try {
      // Usar id ou _id para compatibilidade
      const contactId = contact.id || contact._id;
      if (!contactId) {
        throw new Error('ID do contato não encontrado');
      }

      console.log('🤖 Gerando mensagem de follow-up para:', contact.name);
      const { message } = await apiService.generateFollowUp(contactId);

      console.log('✅ Mensagem de follow-up gerada:', message);

      // Agora enviar a mensagem via WhatsApp
      await handleSendMessage(contact, message);

    } catch (error) {
      toast.error('Erro ao gerar mensagem de acompanhamento');
      console.error('Erro ao gerar mensagem de acompanhamento:', error);
    }
  };

  const handleImportCSV = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setProcessing(true);
      try {
        const reader = new FileReader();
        reader.onload = async (e) => {
          try {
            const csvString = e.target?.result as string;
            const parsedData = parsePregnantWomenCSV(csvString);

            let successCount = 0;
            let errorCount = 0;

            // Processar cada item do CSV usando dados reais
            for (const item of parsedData) {
              if (item.name && item.phone) { // Validação básica
                try {
                  await apiService.createContact({
                    name: item.name,
                    phone: item.phone,
                    email: item.email,
                    babyGender: 'unknown' // CSV não tem gênero do bebê
                  });
                  successCount++;
                } catch (error) {
                  console.error('Erro ao criar contato do CSV:', error);
                  errorCount++;
                }
              } else {
                errorCount++;
              }
            }

            await loadContacts();
            toast.success(`${successCount} contatos importados com sucesso!`);
            if (errorCount > 0) {
              toast.warn(`${errorCount} registros com erro foram ignorados.`);
            }
          } catch (error) {
            console.error('Erro ao processar CSV:', error);
            toast.error('Erro ao processar arquivo CSV');
          } finally {
            setProcessing(false);
          }
        };
        reader.readAsText(file);
      } catch (error) {
        console.error('Erro ao ler arquivo:', error);
        toast.error('Erro ao ler arquivo CSV');
        setProcessing(false);
      }

      if (fileInputRef.current) fileInputRef.current.value = ""; // Reset file input
    }
  };


  if (loading) {
    return <div className="flex justify-center items-center h-full"><Spinner size="lg" /></div>;
  }

  return (
    <div>
      <PageTitle title="Gerenciamento de Gestantes" subtitle="Cadastre, edite e visualize informações das gestantes.">
        <div className="flex space-x-2">
          <input type="file" ref={fileInputRef} onChange={handleFileChange} accept=".csv" className="hidden" />
          <Button onClick={handleImportCSV} variant="outline" disabled={processing}>
            {ICONS.upload} Importar CSV
          </Button>
          <Button onClick={() => handleOpenFormModal()} disabled={processing}>
            {ICONS.add} Nova Gestante
          </Button>
        </div>
      </PageTitle>

      <div className="mb-4">
        <Input 
            placeholder="Buscar por nome, telefone, email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            Icon={() => ICONS.search}
        />
      </div>

      {processing && !loading && <div className="my-4"><Spinner /> <p className="text-center text-sm">Processando...</p></div>}

      <PregnantTable
        contacts={filteredContacts}
        onEdit={handleOpenFormModal}
        onDelete={handleOpenDeleteModal}
        onSendMessage={handleSendMessage}
        onGenerateFollowUp={handleGenerateFollowUp}
        onAudioChat={handleOpenAudioChatModal}
        onAdvancedAudio={handleOpenAdvancedAudioModal}
      />

      <PregnantFormModal
        isOpen={isFormModalOpen}
        onClose={handleCloseFormModal}
        onSave={handleSaveContact}
        contact={selectedContact}
      />

      <DeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={handleDeleteContact}
        contactName={selectedContact?.name}
      />

      {selectedContact && (
        <AudioChatModal
          isOpen={isAudioChatModalOpen}
          onClose={handleCloseAudioChatModal}
          contact={selectedContact}
        />
      )}

      {selectedContact && (
        <AdvancedAudioChatModal
          isOpen={isAdvancedAudioModalOpen}
          onClose={handleCloseAdvancedAudioModal}
          contact={selectedContact}
        />
      )}
    </div>
  );
};

export default PregnantListPage;
