"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.audioQueue = exports.AudioProcessingQueue = void 0;
const events_1 = require("events");
const gemini_1 = require("./gemini");
const contactService_1 = require("./contactService");
const messageService_1 = require("./messageService");
const eventCapture_1 = require("../middleware/eventCapture");
/**
 * Sistema de fila para processamento de áudios em background
 * Garante que mensagens sejam processadas mesmo com alto volume
 */
class AudioProcessingQueue extends events_1.EventEmitter {
    constructor(geminiService) {
        super();
        this.queue = [];
        this.processing = new Map();
        this.isRunning = false;
        this.maxConcurrent = 3;
        this.maxRetries = 3;
        this.processingStats = {
            processed: 0,
            failed: 0,
            avgProcessingTime: 0,
            totalProcessingTime: 0
        };
        this.geminiService = geminiService;
        this.startProcessing();
        // Limpar jobs antigos a cada 30 minutos
        setInterval(() => {
            this.cleanupOldJobs();
        }, 30 * 60 * 1000);
        console.log('🎵 Sistema de fila de áudio inicializado');
    }
    /**
     * Adicionar áudio à fila de processamento
     */
    async addAudioJob(phoneNumber, audioBuffer, mimeType, messageId, priority = 'normal') {
        const jobId = `audio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        // Verificar se contato existe para definir prioridade
        const existingContacts = await contactService_1.contactService.findAll();
        const contactExists = existingContacts.some(contact => contact.phone === phoneNumber);
        const job = {
            id: jobId,
            phoneNumber,
            audioBuffer,
            mimeType,
            messageId,
            timestamp: new Date(),
            retries: 0,
            priority: contactExists ? 'high' : priority, // Priorizar contatos conhecidos
            metadata: {
                isFirstMessage: !contactExists,
                contactExists,
                messageType: mimeType.includes('voice') ? 'voice' : 'audio'
            }
        };
        // Inserir na posição correta baseado na prioridade
        this.insertByPriority(job);
        console.log(`📥 Áudio adicionado à fila: ${jobId}`, {
            phoneNumber,
            priority: job.priority,
            queueSize: this.queue.length,
            processing: this.processing.size
        });
        this.emit('jobAdded', job);
        return jobId;
    }
    /**
     * Inserir job na fila respeitando prioridade
     */
    insertByPriority(job) {
        const priorityOrder = { high: 0, normal: 1, low: 2 };
        let insertIndex = this.queue.length;
        for (let i = 0; i < this.queue.length; i++) {
            if (priorityOrder[job.priority] < priorityOrder[this.queue[i].priority]) {
                insertIndex = i;
                break;
            }
        }
        this.queue.splice(insertIndex, 0, job);
    }
    /**
     * Iniciar processamento contínuo da fila
     */
    startProcessing() {
        if (this.isRunning)
            return;
        this.isRunning = true;
        console.log('🚀 Processamento de fila iniciado');
        // Processar jobs continuamente
        setInterval(async () => {
            await this.processNextJobs();
        }, 1000); // Verificar a cada segundo
    }
    /**
     * Processar próximos jobs da fila
     */
    async processNextJobs() {
        if (this.processing.size >= this.maxConcurrent || this.queue.length === 0) {
            return;
        }
        const availableSlots = this.maxConcurrent - this.processing.size;
        const jobsToProcess = this.queue.splice(0, availableSlots);
        for (const job of jobsToProcess) {
            this.processing.set(job.id, job);
            this.processJob(job).catch(error => {
                console.error(`❌ Erro crítico no processamento do job ${job.id}:`, error);
                this.processing.delete(job.id);
            });
        }
    }
    /**
     * Processar um job individual
     */
    async processJob(job) {
        var _a, _b;
        const startTime = Date.now();
        try {
            console.log(`🎵 Processando áudio: ${job.id}`, {
                phoneNumber: job.phoneNumber,
                attempt: job.retries + 1,
                priority: job.priority
            });
            this.emit('jobStarted', job);
            // 1. Buscar ou criar contato
            let contact = await this.findOrCreateContact(job.phoneNumber);
            // 2. Salvar mensagem de áudio no banco (apenas na primeira tentativa)
            if (job.retries === 0) {
                await messageService_1.messageService.create({
                    contact_id: contact.id,
                    content: '[ÁUDIO RECEBIDO VIA WHATSAPP]',
                    type: 'audio',
                    from_me: false,
                    timestamp: new Date().toISOString(),
                    message_id: job.messageId
                });
            }
            // 3. Processar áudio com Gemini AI
            console.log(`🤖 Enviando áudio para Gemini AI: ${job.id}`);
            const aiResponse = await this.geminiService.generateResponse(contact, 'Mensagem de áudio recebida via WhatsApp', { buffer: job.audioBuffer, mimetype: job.mimeType });
            console.log(`✅ Resposta da IA gerada para ${job.id}:`, aiResponse.response);
            // 4. Salvar resposta da IA no banco
            await messageService_1.messageService.create({
                contact_id: contact.id,
                content: aiResponse.response,
                type: 'text',
                from_me: true,
                timestamp: new Date().toISOString(),
                sentiment: ((_a = aiResponse.sentiment) === null || _a === void 0 ? void 0 : _a.type) || 'neutral'
            });
            // 5. Emitir evento para notificações
            (0, eventCapture_1.emitMessageReceived)({
                contactId: contact.id,
                contactName: contact.name,
                messageType: 'audio',
                aiResponse: aiResponse.response,
                sentiment: ((_b = aiResponse.sentiment) === null || _b === void 0 ? void 0 : _b.type) || 'neutral'
            });
            // 6. Atualizar última interação do contato
            await contactService_1.contactService.update(contact.id, {
                last_interaction: new Date().toISOString()
            });
            const processingTime = Date.now() - startTime;
            // Atualizar estatísticas
            this.updateStats(processingTime, true);
            const result = {
                success: true,
                response: aiResponse.response,
                contactId: contact.id,
                processingTime
            };
            console.log(`✅ Job ${job.id} processado com sucesso em ${processingTime}ms`);
            this.emit('jobCompleted', job, result);
            // Retornar resposta via WhatsApp será feito pelo caller
            this.emit('responseReady', {
                phoneNumber: job.phoneNumber,
                response: aiResponse.response,
                messageId: job.messageId
            });
        }
        catch (error) {
            console.error(`❌ Erro ao processar job ${job.id}:`, error);
            const processingTime = Date.now() - startTime;
            this.updateStats(processingTime, false);
            // Tentar novamente se não excedeu limite
            if (job.retries < this.maxRetries) {
                job.retries++;
                job.timestamp = new Date();
                // Recolocar na fila com prioridade reduzida
                job.priority = job.priority === 'high' ? 'normal' : 'low';
                this.insertByPriority(job);
                console.log(`🔄 Job ${job.id} recolocado na fila (tentativa ${job.retries + 1})`);
                this.emit('jobRetried', job);
            }
            else {
                console.error(`💀 Job ${job.id} falhou definitivamente após ${job.retries} tentativas`);
                const result = {
                    success: false,
                    error: error.message || 'Erro desconhecido',
                    processingTime
                };
                this.emit('jobFailed', job, result);
            }
        }
        finally {
            this.processing.delete(job.id);
        }
    }
    /**
     * Buscar ou criar contato
     */
    async findOrCreateContact(phoneNumber) {
        try {
            // Extrair apenas o número do telefone (remover @c.us)
            const cleanPhone = phoneNumber.replace('@c.us', '');
            console.log(`🔍 Buscando contato para: ${phoneNumber} (limpo: ${cleanPhone})`);
            const existingContacts = await contactService_1.contactService.findAll();
            const existingContact = existingContacts.find(contact => contact.phone === cleanPhone);
            if (existingContact) {
                console.log(`📞 Contato existente encontrado: ${existingContact.name}`);
                return existingContact;
            }
            // Criar novo contato
            console.log(`👤 Criando novo contato para: ${cleanPhone}`);
            const newContact = await contactService_1.contactService.create({
                name: `Contato ${cleanPhone}`,
                phone: cleanPhone,
                baby_gender: 'unknown',
                registration_status: 'unregistered',
                is_active: true,
                created_at: new Date().toISOString(),
                updated_at: new Date().toISOString()
            });
            console.log(`✅ Novo contato criado: ${newContact.id}`);
            return newContact;
        }
        catch (error) {
            console.error('❌ Erro ao buscar/criar contato:', error);
            throw error;
        }
    }
    /**
     * Atualizar estatísticas de processamento
     */
    updateStats(processingTime, success) {
        if (success) {
            this.processingStats.processed++;
        }
        else {
            this.processingStats.failed++;
        }
        this.processingStats.totalProcessingTime += processingTime;
        this.processingStats.avgProcessingTime =
            this.processingStats.totalProcessingTime /
                (this.processingStats.processed + this.processingStats.failed);
    }
    /**
     * Limpar jobs antigos da memória
     */
    cleanupOldJobs() {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        // Remover jobs antigos da fila
        const initialSize = this.queue.length;
        this.queue = this.queue.filter(job => job.timestamp > oneHourAgo);
        const removed = initialSize - this.queue.length;
        if (removed > 0) {
            console.log(`🧹 Removidos ${removed} jobs antigos da fila`);
        }
    }
    /**
     * Obter estatísticas da fila
     */
    getStats() {
        return {
            queue: {
                pending: this.queue.length,
                processing: this.processing.size,
                byPriority: {
                    high: this.queue.filter(j => j.priority === 'high').length,
                    normal: this.queue.filter(j => j.priority === 'normal').length,
                    low: this.queue.filter(j => j.priority === 'low').length
                }
            },
            processing: {
                ...this.processingStats,
                successRate: this.processingStats.processed /
                    (this.processingStats.processed + this.processingStats.failed) * 100
            }
        };
    }
    /**
     * Parar processamento (para shutdown graceful)
     */
    async stop() {
        this.isRunning = false;
        // Aguardar jobs em processamento terminarem
        while (this.processing.size > 0) {
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        console.log('🛑 Sistema de fila de áudio parado');
    }
}
exports.AudioProcessingQueue = AudioProcessingQueue;
// Instância singleton
exports.audioQueue = new AudioProcessingQueue(new gemini_1.GeminiAIService());
