/**
 * TESTE DE ÁUDIO - SISTEMA RAFAELA
 * Testa especificamente o recebimento e processamento de áudios
 */

const https = require('https');
const http = require('http');
const fs = require('fs');

const API_BASE = 'http://localhost:3334/api';

// Função para fazer requisição HTTP
function fazerRequisicao(url, method = 'GET', body = null) {
  return new Promise((resolve) => {
    try {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const httpModule = isHttps ? https : http;
      
      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
      };
      
      const req = httpModule.request(options, (res) => {
        let data = '';
        
        res.on('data', (chunk) => {
          data += chunk;
        });
        
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(data);
            resolve({ 
              success: res.statusCode >= 200 && res.statusCode < 300, 
              data: jsonData, 
              status: res.statusCode 
            });
          } catch (error) {
            resolve({ success: false, error: 'Invalid JSON response', rawData: data });
          }
        });
      });
      
      req.on('error', (error) => {
        resolve({ success: false, error: error.message });
      });
      
      if (body) {
        req.write(JSON.stringify(body));
      }
      
      req.end();
    } catch (error) {
      resolve({ success: false, error: error.message });
    }
  });
}

// Função para aguardar
function aguardar(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Função principal de teste
async function testarSistemaAudio() {
  console.log('🎵 INICIANDO TESTE DO SISTEMA DE ÁUDIO - RAFAELA');
  console.log('=' .repeat(60));
  
  try {
    // 1. Verificar se o backend está respondendo
    console.log('\n🔍 Verificando conectividade...');
    const statusTest = await fazerRequisicao(`${API_BASE}/contacts`);
    
    if (!statusTest.success) {
      console.log('❌ Backend não está respondendo:', statusTest.error);
      return;
    }
    
    console.log('✅ Backend está respondendo');
    
    // 2. Verificar status do WhatsApp
    console.log('\n📱 Verificando status do WhatsApp...');
    await aguardar(2000);
    
    const whatsappStatus = await fazerRequisicao(`${API_BASE}/whatsapp-auto/status`);
    
    if (whatsappStatus.success) {
      const isConnected = whatsappStatus.data?.data?.connection?.isConnected;
      console.log(`📱 WhatsApp: ${isConnected ? '✅ Conectado' : '❌ Desconectado'}`);
      
      if (!isConnected) {
        console.log('⚠️ WhatsApp não está conectado. Conecte primeiro para testar áudios.');
        return;
      }
    } else {
      console.log('❌ Erro ao verificar WhatsApp:', whatsappStatus.error);
      return;
    }
    
    // 3. Verificar gestantes disponíveis
    console.log('\n👥 Verificando gestantes disponíveis...');
    await aguardar(2000);
    
    const gestantesResult = await fazerRequisicao(`${API_BASE}/contacts`);
    
    if (!gestantesResult.success) {
      console.log('❌ Erro ao buscar gestantes:', gestantesResult.error);
      return;
    }
    
    const gestantes = gestantesResult.data;
    console.log(`✅ ${gestantes.length} gestantes encontradas`);
    
    if (gestantes.length === 0) {
      console.log('⚠️ Nenhuma gestante disponível. Criando uma para teste...');
      
      const novaGestante = {
        name: 'Teste Áudio',
        phone: '(84) 99999-9999',
        email: '<EMAIL>',
        birth_date: '1990-01-01',
        due_date: '2024-12-31',
        baby_gender: 'female',
        is_active: true,
        registration_status: 'registered'
      };
      
      await aguardar(2000);
      const criarResult = await fazerRequisicao(`${API_BASE}/contacts`, 'POST', novaGestante);
      
      if (criarResult.success) {
        console.log('✅ Gestante de teste criada');
        gestantes.push(criarResult.data);
      } else {
        console.log('❌ Erro ao criar gestante de teste:', criarResult.error);
        return;
      }
    }
    
    // 4. Simular recebimento de áudio (criar mensagem de áudio no histórico)
    console.log('\n🎵 Simulando recebimento de áudio...');
    const gestante = gestantes[0];
    
    await aguardar(2000);
    const audioMessage = {
      content: '🎵 [ÁUDIO RECEBIDO VIA WHATSAPP] - Teste do sistema',
      type: 'audio',
      fromMe: false
    };
    
    const audioResult = await fazerRequisicao(
      `${API_BASE}/contacts/${gestante.id}/messages`,
      'POST',
      audioMessage
    );
    
    if (audioResult.success) {
      console.log('✅ Mensagem de áudio simulada criada no histórico');
    } else {
      console.log('❌ Erro ao criar mensagem de áudio:', audioResult.error);
    }
    
    // 5. Verificar se a IA está funcionando para áudios
    console.log('\n🤖 Testando IA para processamento de áudio...');
    await aguardar(2000);
    
    const iaAudioTest = await fazerRequisicao(
      `${API_BASE}/ai/generate-suggestion`,
      'POST',
      {
        prompt: 'Uma gestante enviou um áudio dizendo que está sentindo o bebê mexer muito. Responda de forma carinhosa.',
        systemInstruction: 'Você é Rafaela, assistente virtual para gestantes. Seja empática e carinhosa.',
        promptType: 'empathy',
        context: 'Mensagem de áudio recebida via WhatsApp'
      }
    );
    
    if (iaAudioTest.success) {
      console.log('✅ IA funcionando para áudios:');
      console.log(`"${iaAudioTest.data.suggestion}"`);
      
      // Salvar resposta da IA como mensagem
      await aguardar(1000);
      const iaResponse = {
        content: iaAudioTest.data.suggestion,
        type: 'text',
        fromMe: true
      };
      
      const iaMessageResult = await fazerRequisicao(
        `${API_BASE}/contacts/${gestante.id}/messages`,
        'POST',
        iaResponse
      );
      
      if (iaMessageResult.success) {
        console.log('✅ Resposta da IA salva no histórico');
      }
    } else {
      console.log('❌ Erro na IA para áudios:', iaAudioTest.error);
    }
    
    // 6. Verificar histórico de mensagens (incluindo áudios)
    console.log('\n📚 Verificando histórico com mensagens de áudio...');
    await aguardar(2000);
    
    const historicoResult = await fazerRequisicao(`${API_BASE}/contacts/${gestante.id}/messages`);
    
    if (historicoResult.success) {
      const mensagens = historicoResult.data.data || [];
      const mensagensAudio = mensagens.filter(m => m.type === 'audio');
      
      console.log(`📱 ${mensagens.length} mensagens no histórico`);
      console.log(`🎵 ${mensagensAudio.length} mensagens de áudio`);
      
      if (mensagensAudio.length > 0) {
        console.log('\n🎵 Mensagens de áudio encontradas:');
        mensagensAudio.forEach((msg, index) => {
          const timestamp = new Date(msg.timestamp).toLocaleTimeString('pt-BR');
          console.log(`  ${index + 1}. [${timestamp}] ${msg.content}`);
        });
      }
    } else {
      console.log('❌ Erro ao verificar histórico:', historicoResult.error);
    }
    
    // 7. Instruções para teste real
    console.log('\n📱 INSTRUÇÕES PARA TESTE REAL COM ÁUDIO:');
    console.log('=' .repeat(60));
    console.log('1. 📱 Abra o WhatsApp no seu celular');
    console.log('2. 🎤 Grave uma mensagem de voz');
    console.log('3. 📤 Envie para o número conectado ao sistema');
    console.log('4. ⏱️  Aguarde alguns segundos');
    console.log('5. 🤖 O sistema deve processar automaticamente e responder');
    console.log('6. 💬 Verifique se a resposta chegou no WhatsApp');
    console.log('7. 📊 Monitore os logs do backend para ver o processamento');
    
    // Relatório final
    console.log('\n🎉 TESTE DO SISTEMA DE ÁUDIO CONCLUÍDO!');
    console.log('=' .repeat(60));
    console.log(`📱 WhatsApp: ${whatsappStatus.success ? '✅' : '❌'}`);
    console.log(`👥 Gestantes: ${gestantes.length} disponíveis`);
    console.log(`🤖 IA para áudios: ${iaAudioTest.success ? '✅' : '❌'}`);
    console.log(`📚 Histórico: ${historicoResult.success ? '✅' : '❌'}`);
    
    const sistemaOk = whatsappStatus.success && 
                     gestantes.length > 0 && 
                     iaAudioTest.success && 
                     historicoResult.success;
    
    console.log(`\n🎯 SISTEMA DE ÁUDIO: ${sistemaOk ? '✅ TOTALMENTE FUNCIONAL' : '⚠️ PRECISA AJUSTES'}`);
    
    if (sistemaOk) {
      console.log('🎊 O sistema está pronto para receber e processar áudios!');
      console.log('🎤 Envie um áudio via WhatsApp para testar em tempo real');
      console.log('🤖 A IA processará automaticamente e responderá');
    }
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

// Executar se chamado diretamente
if (require.main === module) {
  testarSistemaAudio()
    .catch(error => {
      console.error('❌ Erro no teste:', error);
    });
}

module.exports = { testarSistemaAudio };
