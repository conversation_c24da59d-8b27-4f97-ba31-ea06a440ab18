"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
const Contact_1 = require("../../models/Contact");
const Message_1 = require("../../models/Message");
describe('Rotas de Analytics', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    beforeEach(() => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
    });
    describe('GET /api/analytics/dashboard', () => {
        it('deve retornar analytics do dashboard com dados corretos', async () => {
            // Limpar contatos e mensagens existentes primeiro
            await Contact_1.Contact.deleteMany({});
            await Message_1.Message.deleteMany({});
            // Criar contatos de teste com diferentes gêneros
            const contact1 = await (0, testHelpers_1.createTestContact)({ name: 'Ana', babyGender: 'female', isActive: true });
            const contact2 = await (0, testHelpers_1.createTestContact)({ name: 'Maria', babyGender: 'female', isActive: true });
            const contact3 = await (0, testHelpers_1.createTestContact)({ name: 'João', babyGender: 'male', isActive: true });
            const contact4 = await (0, testHelpers_1.createTestContact)({ name: 'Pedro', babyGender: 'unknown', isActive: true });
            const contact5 = await (0, testHelpers_1.createTestContact)({ name: 'Inativo', babyGender: 'male', isActive: false });
            // Criar mensagens de teste usando os contatos criados
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            await (0, testHelpers_1.createTestMessage)({
                contact: contact1._id,
                content: 'Mensagem de ontem',
                timestamp: yesterday
            });
            const twoDaysAgo = new Date();
            twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
            await (0, testHelpers_1.createTestMessage)({
                contact: contact2._id,
                content: 'Mensagem de anteontem',
                timestamp: twoDaysAgo
            });
            // Criar mensagem de hoje
            const today = new Date();
            today.setHours(10, 0, 0, 0);
            await (0, testHelpers_1.createTestMessage)({
                contact: contact3._id,
                content: 'Mensagem de hoje',
                timestamp: today
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body).toHaveProperty('totalContacts');
            expect(response.body).toHaveProperty('babyGenders');
            expect(response.body).toHaveProperty('messages');
            expect(response.body).toHaveProperty('lastUpdated');
            // Verificar contatos ativos (4 ativos, 1 inativo)
            expect(response.body.totalContacts).toBe(4);
            // Verificar distribuição por gênero
            expect(response.body.babyGenders.female).toBe(2);
            expect(response.body.babyGenders.male).toBe(1);
            expect(response.body.babyGenders.unknown).toBe(1);
            // Verificar mensagens
            expect(response.body.messages.total).toBe(3);
            expect(response.body.messages.today).toBe(1);
        });
        it('deve retornar zeros quando não há dados', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.totalContacts).toBe(0);
            expect(response.body.babyGenders.female).toBe(0);
            expect(response.body.babyGenders.male).toBe(0);
            expect(response.body.babyGenders.unknown).toBe(0);
            expect(response.body.messages.total).toBe(0);
            expect(response.body.messages.today).toBe(0);
        });
        it('deve contar apenas contatos ativos', async () => {
            // Criar contatos ativos e inativos
            await (0, testHelpers_1.createTestContact)({ name: 'Ativo 1', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'Ativo 2', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'Inativo 1', isActive: false });
            await (0, testHelpers_1.createTestContact)({ name: 'Inativo 2', isActive: false });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.totalContacts).toBe(2);
        });
        it('deve contar mensagens de hoje corretamente', async () => {
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            // Mensagem de hoje
            today.setHours(10, 0, 0, 0);
            await (0, testHelpers_1.createTestMessage)({
                content: 'Mensagem de hoje',
                timestamp: today
            });
            // Mensagem de ontem
            await (0, testHelpers_1.createTestMessage)({
                content: 'Mensagem de ontem',
                timestamp: yesterday
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.messages.total).toBe(2);
            expect(response.body.messages.today).toBe(1);
        });
        it('deve incluir timestamp da última atualização', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.lastUpdated).toBeDefined();
            expect(new Date(response.body.lastUpdated)).toBeInstanceOf(Date);
        });
        it('deve tratar erro de banco de dados', async () => {
            // Simular erro fechando a conexão
            const originalCountDocuments = Contact_1.Contact.countDocuments;
            Contact_1.Contact.countDocuments = jest.fn().mockRejectedValue(new Error('Erro de conexão'));
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(500);
            expect(response.body).toHaveProperty('error');
            // Restaurar método original
            Contact_1.Contact.countDocuments = originalCountDocuments;
        });
    });
    describe('Validação de dados de analytics', () => {
        it('deve calcular estatísticas por gênero corretamente', async () => {
            // Criar contatos com diferentes gêneros
            await (0, testHelpers_1.createTestContact)({ babyGender: 'male' });
            await (0, testHelpers_1.createTestContact)({ babyGender: 'male' });
            await (0, testHelpers_1.createTestContact)({ babyGender: 'female' });
            await (0, testHelpers_1.createTestContact)({ babyGender: 'unknown' });
            await (0, testHelpers_1.createTestContact)({ babyGender: 'female' });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.babyGenders.male).toBe(2);
            expect(response.body.babyGenders.female).toBe(2);
            expect(response.body.babyGenders.unknown).toBe(1);
        });
        it('deve ignorar contatos com gênero inválido', async () => {
            // Criar contato com gênero válido mas não contabilizado
            const contact = await (0, testHelpers_1.createTestContact)({
                name: 'Teste',
                babyGender: 'unknown'
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            // O contato deve ser contado no total e nas estatísticas de gênero
            expect(response.body.totalContacts).toBe(1);
            expect(response.body.babyGenders.male).toBe(0);
            expect(response.body.babyGenders.female).toBe(0);
            expect(response.body.babyGenders.unknown).toBe(1);
        });
        it('deve contar mensagens em diferentes fusos horários', async () => {
            const now = new Date();
            // Mensagem no início do dia
            const startOfDay = new Date(now);
            startOfDay.setHours(0, 0, 0, 0);
            await (0, testHelpers_1.createTestMessage)({ timestamp: startOfDay });
            // Mensagem no final do dia
            const endOfDay = new Date(now);
            endOfDay.setHours(23, 59, 59, 999);
            await (0, testHelpers_1.createTestMessage)({ timestamp: endOfDay });
            const response = await (0, supertest_1.default)(app)
                .get('/api/analytics/dashboard')
                .expect(200);
            expect(response.body.messages.today).toBe(2);
        });
    });
});
