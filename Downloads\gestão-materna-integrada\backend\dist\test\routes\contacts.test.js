"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
const Contact_1 = require("../../models/Contact");
describe('Rotas de Contatos', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    beforeEach(() => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
    });
    describe('GET /api/contacts', () => {
        it('deve listar contatos ativos', async () => {
            // Criar contatos de teste
            await (0, testHelpers_1.createTestContact)({ name: '<PERSON> <PERSON>', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'Maria <PERSON>', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'João Inativo', isActive: false });
            const response = await (0, supertest_1.default)(app)
                .get('/api/contacts')
                .expect(200);
            expect(response.body).toHaveLength(2);
            expect(response.body[0]).toHaveProperty('name');
            expect(response.body[0]).toHaveProperty('phone');
            expect(response.body[0]).toHaveProperty('babyGender');
            expect(response.body[0].isActive).toBe(true);
        });
        it('deve retornar array vazio quando não há contatos', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/contacts')
                .expect(200);
            expect(response.body).toEqual([]);
        });
        it('deve limitar a 100 contatos', async () => {
            // Criar 150 contatos
            const promises = Array.from({ length: 150 }, (_, i) => (0, testHelpers_1.createTestContact)({
                name: `Contato ${i}`,
                phone: `+551199999${i.toString().padStart(4, '0')}`
            }));
            await Promise.all(promises);
            const response = await (0, supertest_1.default)(app)
                .get('/api/contacts')
                .expect(200);
            expect(response.body).toHaveLength(100);
        });
    });
    describe('POST /api/contacts', () => {
        it('deve criar novo contato com dados válidos', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send(testHelpers_1.validContactData)
                .expect(201);
            expect(response.body).toHaveProperty('_id');
            expect(response.body.name).toBe(testHelpers_1.validContactData.name);
            expect(response.body.phone).toBe(testHelpers_1.validContactData.phone);
            expect(response.body.babyGender).toBe(testHelpers_1.validContactData.babyGender);
            expect(response.body.isActive).toBe(true);
            // Verificar se foi salvo no banco
            const savedContact = await Contact_1.Contact.findById(response.body._id);
            expect(savedContact).toBeTruthy();
        });
        it('deve rejeitar contato sem nome', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send({ ...testHelpers_1.validContactData, name: '' })
                .expect(400);
            expect(response.body).toHaveProperty('error');
            expect(response.body.code).toBe('MISSING_REQUIRED_FIELDS');
        });
        it('deve rejeitar contato sem telefone', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send({ ...testHelpers_1.validContactData, phone: '' })
                .expect(400);
            expect(response.body).toHaveProperty('error');
            expect(response.body.code).toBe('MISSING_REQUIRED_FIELDS');
        });
        it('deve rejeitar telefone duplicado para contatos ativos', async () => {
            // Criar contato existente
            await (0, testHelpers_1.createTestContact)({ phone: testHelpers_1.validContactData.phone });
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send(testHelpers_1.validContactData)
                .expect(409);
            expect(response.body.code).toBe('PHONE_ALREADY_EXISTS');
        });
        it('deve reativar contato inativo com mesmo telefone', async () => {
            // Criar contato inativo
            const inactiveContact = await (0, testHelpers_1.createTestContact)({
                phone: testHelpers_1.validContactData.phone,
                isActive: false
            });
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send(testHelpers_1.validContactData)
                .expect(201);
            expect(response.body._id).toBe(inactiveContact._id.toString());
            expect(response.body.isActive).toBe(true);
            expect(response.body.name).toBe(testHelpers_1.validContactData.name);
        });
        it('deve definir babyGender como unknown se não fornecido', async () => {
            const { babyGender, ...dataWithoutGender } = testHelpers_1.validContactData;
            const response = await (0, supertest_1.default)(app)
                .post('/api/contacts')
                .send(dataWithoutGender)
                .expect(201);
            expect(response.body.babyGender).toBe('unknown');
        });
    });
    describe('PUT /api/contacts/:id', () => {
        it('deve atualizar contato existente', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const updateData = {
                name: 'Nome Atualizado',
                phone: '+5511777777777',
                babyGender: 'male'
            };
            const response = await (0, supertest_1.default)(app)
                .put(`/api/contacts/${contact._id}`)
                .send(updateData)
                .expect(200);
            expect(response.body.name).toBe(updateData.name);
            expect(response.body.phone).toBe(updateData.phone);
            expect(response.body.babyGender).toBe(updateData.babyGender);
        });
        it('deve retornar 404 para contato inexistente', async () => {
            const fakeId = '507f1f77bcf86cd799439011';
            const response = await (0, supertest_1.default)(app)
                .put(`/api/contacts/${fakeId}`)
                .send(testHelpers_1.validContactData)
                .expect(404);
            expect(response.body.code).toBe('NOT_FOUND');
        });
        it('deve rejeitar dados inválidos na atualização', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const response = await (0, supertest_1.default)(app)
                .put(`/api/contacts/${contact._id}`)
                .send(testHelpers_1.invalidContactData)
                .expect(400);
            expect(response.body.code).toBe('MISSING_REQUIRED_FIELDS');
        });
    });
    describe('DELETE /api/contacts/:id', () => {
        it('deve fazer soft delete por padrão', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/contacts/${contact._id}`)
                .expect(200);
            expect(response.body.message).toContain('deletada com sucesso');
            // Verificar se ainda existe no banco mas inativo
            const deletedContact = await Contact_1.Contact.findById(contact._id);
            expect(deletedContact).toBeTruthy();
            expect(deletedContact.isActive).toBe(false);
        });
        it('deve fazer hard delete quando solicitado', async () => {
            const contact = await (0, testHelpers_1.createTestContact)();
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/contacts/${contact._id}?hard=true`)
                .expect(200);
            expect(response.body.message).toContain('removida permanentemente');
            // Verificar se foi removido do banco
            const deletedContact = await Contact_1.Contact.findById(contact._id);
            expect(deletedContact).toBeNull();
        });
        it('deve retornar 404 para contato inexistente', async () => {
            const fakeId = '507f1f77bcf86cd799439011';
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/contacts/${fakeId}`)
                .expect(404);
            expect(response.body.code).toBe('NOT_FOUND');
        });
    });
    describe('DELETE /api/contacts/cleanup/inactive', () => {
        it('deve remover todos os contatos inativos', async () => {
            // Criar contatos ativos e inativos
            await (0, testHelpers_1.createTestContact)({ name: 'Ativo 1', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'Ativo 2', isActive: true });
            await (0, testHelpers_1.createTestContact)({ name: 'Inativo 1', isActive: false });
            await (0, testHelpers_1.createTestContact)({ name: 'Inativo 2', isActive: false });
            const response = await (0, supertest_1.default)(app)
                .delete('/api/contacts/cleanup/inactive')
                .expect(200);
            expect(response.body.deletedCount).toBe(2);
            // Verificar se apenas contatos ativos restaram
            const remainingContacts = await Contact_1.Contact.find({});
            expect(remainingContacts).toHaveLength(2);
            expect(remainingContacts.every(c => c.isActive)).toBe(true);
        });
    });
});
