"use strict";
// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use messageTemplateService do Supabase em vez deste modelo
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageTemplate = void 0;
// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use messageTemplateService do Supabase para todas as operações
// MODELO REMOVIDO - Use messageTemplateService do Supabase
exports.MessageTemplate = {
    find: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    findById: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    findOne: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    create: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    countDocuments: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    aggregate: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    updateMany: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    deleteMany: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); },
    findByIdAndDelete: () => { throw new Error('MongoDB removido - Use messageTemplateService do Supabase'); }
};
