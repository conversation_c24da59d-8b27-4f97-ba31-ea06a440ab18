"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const healthMonitor_1 = __importDefault(require("../services/healthMonitor"));
const router = (0, express_1.Router)();
// GET /api/health - Health check básico (público)
router.get('/', async (req, res) => {
    try {
        const health = healthMonitor_1.default.getSystemHealth();
        const stats = healthMonitor_1.default.getStats();
        // Resposta básica para health check público
        const basicHealth = {
            status: health.overall,
            timestamp: health.timestamp,
            uptime: health.uptime,
            version: process.env.npm_package_version || '1.0.0',
            environment: process.env.NODE_ENV || 'development'
        };
        // Status HTTP baseado na saúde
        const statusCode = health.overall === 'healthy' ? 200 :
            health.overall === 'warning' ? 200 : 503;
        res.status(statusCode).json(basicHealth);
    }
    catch (error) {
        console.error('Erro no health check:', error);
        res.status(503).json({
            status: 'critical',
            error: 'Erro interno do servidor',
            timestamp: new Date().toISOString()
        });
    }
});
// GET /api/health/detailed - Health check detalhado (autenticado)
router.get('/detailed', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const health = healthMonitor_1.default.getSystemHealth();
        const stats = healthMonitor_1.default.getStats();
        res.json({
            ...health,
            stats,
            monitoring: {
                active: stats.monitoring,
                checksCount: stats.totalChecks,
                metricsCount: stats.totalMetrics
            }
        });
    }
    catch (error) {
        console.error('Erro ao obter health detalhado:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/health/checks - Listar verificações de saúde
router.get('/checks', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const health = healthMonitor_1.default.getSystemHealth();
        res.json({
            checks: health.checks,
            summary: {
                total: health.checks.length,
                healthy: health.checks.filter(c => c.status === 'healthy').length,
                warning: health.checks.filter(c => c.status === 'warning').length,
                critical: health.checks.filter(c => c.status === 'critical').length
            },
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao listar checks:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/health/checks/:id/run - Executar verificação específica
router.post('/checks/:id/run', auth_1.authenticate, auth_1.requireAdminOrCoordinator, async (req, res) => {
    try {
        const { id } = req.params;
        const result = await healthMonitor_1.default.runCheck(id);
        if (!result) {
            return res.status(404).json({
                error: 'Verificação não encontrada',
                code: 'CHECK_NOT_FOUND'
            });
        }
        res.json({
            message: 'Verificação executada com sucesso',
            check: result,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao executar check:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/health/metrics - Obter métricas do sistema
router.get('/metrics', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), [
    (0, express_validator_1.query)('type')
        .optional()
        .isIn(['memory_heap_used', 'cpu_usage', 'uptime'])
        .withMessage('Tipo de métrica inválido'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 500 })
        .withMessage('Limite deve ser entre 1 e 500')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { type, limit } = req.query;
        const limitNum = limit ? parseInt(limit) : 50;
        let metrics;
        if (type) {
            metrics = healthMonitor_1.default.getMetricsByType(type, limitNum);
        }
        else {
            const health = healthMonitor_1.default.getSystemHealth();
            metrics = health.metrics;
        }
        res.json({
            metrics,
            count: metrics.length,
            type: type || 'all',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao obter métricas:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/health/monitoring/start - Iniciar monitoramento
router.post('/monitoring/start', auth_1.authenticate, auth_1.requireAdminOrCoordinator, [
    (0, express_validator_1.query)('interval')
        .optional()
        .isInt({ min: 5000, max: 300000 })
        .withMessage('Intervalo deve ser entre 5000ms e 300000ms')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const { interval } = req.query;
        const intervalMs = interval ? parseInt(interval) : 30000;
        healthMonitor_1.default.startMonitoring(intervalMs);
        res.json({
            message: 'Monitoramento iniciado com sucesso',
            interval: intervalMs,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao iniciar monitoramento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// POST /api/health/monitoring/stop - Parar monitoramento
router.post('/monitoring/stop', auth_1.authenticate, auth_1.requireAdminOrCoordinator, async (req, res) => {
    try {
        healthMonitor_1.default.stopMonitoring();
        res.json({
            message: 'Monitoramento parado com sucesso',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao parar monitoramento:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/health/stats - Estatísticas do monitoramento
router.get('/stats', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        const stats = healthMonitor_1.default.getStats();
        const health = healthMonitor_1.default.getSystemHealth();
        const detailedStats = {
            ...stats,
            overall: health.overall,
            lastUpdate: health.timestamp,
            checksBreakdown: {
                healthy: stats.healthyChecks,
                warning: stats.warningChecks,
                critical: stats.criticalChecks
            },
            systemInfo: {
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                pid: process.pid
            }
        };
        res.json({
            stats: detailedStats,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        console.error('Erro ao obter estatísticas:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/health/live - Endpoint para monitoramento em tempo real (SSE)
router.get('/live', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), async (req, res) => {
    try {
        // Configurar Server-Sent Events
        res.writeHead(200, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Headers': 'Cache-Control'
        });
        // Enviar dados iniciais
        const initialHealth = healthMonitor_1.default.getSystemHealth();
        res.write(`data: ${JSON.stringify(initialHealth)}\n\n`);
        // Listener para atualizações de saúde
        const healthUpdateListener = (health) => {
            res.write(`data: ${JSON.stringify(health)}\n\n`);
        };
        healthMonitor_1.default.on('health_updated', healthUpdateListener);
        // Cleanup quando a conexão for fechada
        req.on('close', () => {
            healthMonitor_1.default.removeListener('health_updated', healthUpdateListener);
            res.end();
        });
        // Heartbeat a cada 30 segundos
        const heartbeat = setInterval(() => {
            res.write(`: heartbeat\n\n`);
        }, 30000);
        req.on('close', () => {
            clearInterval(heartbeat);
        });
    }
    catch (error) {
        console.error('Erro no endpoint live:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
