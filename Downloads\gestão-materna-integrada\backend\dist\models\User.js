"use strict";
// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use userService do Supabase em vez deste modelo
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use userService do Supabase para todas as operações
// MODELO REMOVIDO - Use userService do Supabase
exports.User = {
    find: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
    findById: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
    findOne: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
    create: () => { throw new Error('MongoDB removido - Use userService do Supabase'); },
    findByCredentials: () => { throw new Error('MongoDB removido - Use userService.authenticate do Supabase'); }
};
