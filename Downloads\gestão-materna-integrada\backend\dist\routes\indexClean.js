"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.setupRoutes = setupRoutes;
// Importar rotas modulares
const auth_1 = __importDefault(require("./auth"));
const messages_1 = __importDefault(require("./messages"));
const analytics_1 = __importDefault(require("./analytics"));
const whatsapp_1 = __importStar(require("./whatsapp"));
const schedules_1 = __importDefault(require("./schedules"));
const templates_1 = __importDefault(require("./templates"));
const webhooks_1 = __importDefault(require("./webhooks"));
const health_1 = __importDefault(require("./health"));
// Importar rotas Supabase
const supabaseRoutes_1 = require("./supabaseRoutes");
function setupRoutes(app, whatsappClient, geminiService) {
    // Configurar WhatsApp client nas rotas
    (0, whatsapp_1.setWhatsAppClient)(whatsappClient);
    // =====================================================
    // ROTAS SUPABASE (SUBSTITUI MONGODB)
    // =====================================================
    (0, supabaseRoutes_1.setupSupabaseRoutes)(app);
    // =====================================================
    // ROTAS COM AUTENTICAÇÃO
    // =====================================================
    // Rotas de autenticação
    app.use('/api/auth', auth_1.default);
    // Rotas de mensagens
    app.use('/api/messages', messages_1.default);
    // Rotas de analytics
    app.use('/api/analytics', analytics_1.default);
    // Rotas do WhatsApp
    app.use('/api/whatsapp', whatsapp_1.default);
    // Rotas de agendamentos proativos
    app.use('/api/schedules', schedules_1.default);
    // Rotas de templates de mensagens
    app.use('/api/templates', templates_1.default);
    // Rotas de webhooks
    app.use('/api/webhooks', webhooks_1.default);
    // Rotas de monitoramento de saúde
    app.use('/api/health', health_1.default);
    // =====================================================
    // ROTAS DE UTILIDADE
    // =====================================================
    // Rota de health check básico
    app.get('/api/health', (req, res) => {
        res.json({
            status: 'ok',
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            environment: process.env.NODE_ENV || 'development',
            database: 'Supabase',
            mongodb_removed: true
        });
    });
    // Rota para processar mensagem com IA
    app.post('/api/ai/process', async (req, res) => {
        try {
            const { message, contactId } = req.body;
            if (!contactId) {
                return res.status(400).json({ error: 'ID do contato é obrigatório' });
            }
            // Processar com IA usando Gemini
            const aiResponse = await geminiService.processMessage(message, contactId);
            res.json({
                originalMessage: message,
                aiResponse: aiResponse,
                contactId: contactId,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            console.error('❌ Erro ao processar mensagem com IA:', error);
            res.status(500).json({ error: error.message });
        }
    });
    // Rota para verificar status do sistema
    app.get('/api/system/status', (req, res) => {
        res.json({
            status: 'operational',
            database: 'Supabase',
            mongodb_removed: true,
            whatsapp: 'connected',
            ai: 'gemini',
            version: '2.0.0',
            migration_completed: true,
            timestamp: new Date().toISOString()
        });
    });
    console.log('✅ Rotas configuradas com sucesso - MongoDB removido, Supabase ativo');
}
