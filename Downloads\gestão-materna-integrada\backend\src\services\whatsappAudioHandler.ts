import { GeminiAIService } from './gemini';
import { contactService } from './contactService';
import { messageService } from './messageService';
import { emitMessageReceived } from '../middleware/eventCapture';
import { audioQueue } from './audioQueue';

/**
 * Serviço para processar mensagens de áudio recebidas via WhatsApp
 * Este serviço será integrado com o wppconnect para processar áudios automaticamente
 */
export class WhatsAppAudioHandler {
  private geminiService: GeminiAIService;

  constructor(geminiService: GeminiAIService) {
    this.geminiService = geminiService;
  }

  /**
   * Processa uma mensagem de áudio recebida via WhatsApp
   * @param phoneNumber - Número de telefone do remetente
   * @param audioBuffer - Buffer do arquivo de áudio
   * @param mimeType - Tipo MIME do áudio
   * @param messageId - ID da mensagem no WhatsApp
   */
  async processIncomingAudio(
    phoneNumber: string,
    audioBuffer: Buffer,
    mimeType: string,
    messageId: string
  ): Promise<{
    success: boolean;
    response?: string;
    error?: string;
  }> {
    try {
      console.log('🎵 Processando áudio recebido via WhatsApp:', {
        phoneNumber,
        mimeType,
        audioSize: audioBuffer.length,
        messageId
      });

      // 1. Buscar ou criar contato
      let contact = await this.findOrCreateContact(phoneNumber);
      
      // 2. Salvar mensagem de áudio no banco
      await messageService.create({
        contact_id: contact.id,
        content: '[ÁUDIO RECEBIDO VIA WHATSAPP]',
        type: 'audio',
        from_me: false,
        timestamp: new Date().toISOString(),
        message_id: messageId
      });

      // 3. Processar áudio com Gemini AI
      console.log('🤖 Enviando áudio para Gemini AI...');
      const aiResponse = await this.geminiService.generateResponse(
        contact,
        'Mensagem de áudio recebida via WhatsApp',
        { buffer: audioBuffer, mimetype: mimeType }
      );

      console.log('✅ Resposta da IA gerada:', aiResponse.response);

      // 4. Salvar resposta da IA no banco
      await messageService.create({
        contact_id: contact.id,
        content: aiResponse.response,
        type: 'text',
        from_me: true,
        timestamp: new Date().toISOString(),
        sentiment: aiResponse.sentiment?.type || 'neutral'
      });

      // 5. Emitir evento para notificações
      emitMessageReceived({
        contactId: contact.id,
        contactName: contact.name,
        messageType: 'audio',
        aiResponse: aiResponse.response,
        sentiment: aiResponse.sentiment?.type || 'neutral'
      });

      // 6. Atualizar última interação do contato
      await contactService.update(contact.id, {
        last_interaction: new Date().toISOString()
      });

      return {
        success: true,
        response: aiResponse.response
      };

    } catch (error: any) {
      console.error('❌ Erro ao processar áudio do WhatsApp:', error);
      
      return {
        success: false,
        error: error.message || 'Erro interno ao processar áudio'
      };
    }
  }

  /**
   * Busca um contato existente ou cria um novo baseado no número de telefone
   */
  private async findOrCreateContact(phoneNumber: string) {
    try {
      // Tentar encontrar contato existente
      const existingContacts = await contactService.findAll();
      const existingContact = existingContacts.find(
        contact => contact.phone === phoneNumber
      );

      if (existingContact) {
        console.log('📞 Contato existente encontrado:', existingContact.name);
        return existingContact;
      }

      // Criar novo contato se não existir
      console.log('👤 Criando novo contato para:', phoneNumber);
      const newContact = await contactService.create({
        name: `Contato ${phoneNumber}`,
        phone: phoneNumber,
        baby_gender: 'unknown',
        registration_status: 'unregistered',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      console.log('✅ Novo contato criado:', newContact.id);
      return newContact;

    } catch (error) {
      console.error('❌ Erro ao buscar/criar contato:', error);
      throw error;
    }
  }

  /**
   * Valida se o arquivo de áudio é suportado
   */
  static isValidAudioType(mimeType: string): boolean {
    const supportedTypes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/ogg',
      'audio/webm',
      'audio/m4a',
      'audio/aac'
    ];
    
    return supportedTypes.includes(mimeType.toLowerCase());
  }

  /**
   * Converte áudio para formato suportado se necessário
   * (Implementação futura - pode usar ffmpeg ou similar)
   */
  static async convertAudioIfNeeded(
    audioBuffer: Buffer,
    mimeType: string
  ): Promise<{ buffer: Buffer; mimeType: string }> {
    // Por enquanto, retorna o áudio original
    // No futuro, pode implementar conversão usando ffmpeg
    return { buffer: audioBuffer, mimeType };
  }
}

/**
 * Sistema automático de processamento de áudios do WhatsApp
 * Usa fila em background para garantir processamento mesmo com alto volume
 */
export class AutomaticWhatsAppHandler {
  private client: any;
  private isActive: boolean = false;
  private messageHandlers: Map<string, NodeJS.Timeout> = new Map();

  constructor(client: any) {
    this.client = client;
    this.setupEventListeners();
  }

  /**
   * Configurar listeners automáticos
   */
  private setupEventListeners(): void {
    // Listener para respostas prontas da fila
    audioQueue.on('responseReady', async (data) => {
      await this.sendWhatsAppResponse(data.phoneNumber, data.response, data.messageId);
    });

    // Listener para falhas críticas
    audioQueue.on('jobFailed', async (job, result) => {
      await this.sendErrorMessage(job.phoneNumber, job.messageId);
    });

    console.log('🎵 Listeners automáticos do WhatsApp configurados');
  }

  /**
   * Ativar processamento automático
   */
  public activate(): void {
    this.isActive = true;
    console.log('✅ Processamento automático de áudios ativado');
  }

  /**
   * Desativar processamento automático
   */
  public deactivate(): void {
    this.isActive = false;
    console.log('🛑 Processamento automático de áudios desativado');
  }

  /**
   * Baixar áudio com múltiplas tentativas
   */
  private async downloadAudioWithRetry(message: any, maxRetries: number = 3): Promise<{ buffer: Buffer; mimeType: string } | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📥 Tentativa ${attempt}/${maxRetries} de download do áudio...`);

        const audioData = await this.client.downloadMedia(message);

        if (!audioData) {
          console.warn(`⚠️ Tentativa ${attempt}: downloadMedia retornou null`);
          continue;
        }

        // Verificar se audioData é um array de bytes, objeto, string ou buffer
        let audioBuffer: Buffer;
        let mimeType: string;

        if (typeof audioData === 'string') {
          // audioData é uma string (provavelmente base64)
          try {
            console.log(`🔍 Tentativa ${attempt}: String recebida, tamanho: ${audioData.length}, primeiros 50 chars:`, audioData.substring(0, 50));

            // Verificar se é base64 válido
            if (audioData.length === 0) {
              console.warn(`⚠️ Tentativa ${attempt}: String vazia`);
              continue;
            }

            audioBuffer = Buffer.from(audioData, 'base64');
            mimeType = 'audio/ogg'; // Padrão para WhatsApp
            console.log(`✅ Áudio como string base64 na tentativa ${attempt}:`, {
              size: audioBuffer.length,
              type: 'base64_string',
              originalStringLength: audioData.length
            });
          } catch (error) {
            console.warn(`⚠️ Tentativa ${attempt}: Erro ao converter string base64:`, error);
            console.warn(`⚠️ String problemática (primeiros 100 chars):`, audioData.substring(0, 100));
            continue;
          }
        } else if (Array.isArray(audioData) || Buffer.isBuffer(audioData)) {
          // audioData é diretamente os bytes do arquivo
          audioBuffer = Buffer.isBuffer(audioData) ? audioData : Buffer.from(audioData);
          mimeType = 'audio/ogg'; // Padrão para WhatsApp
          console.log(`✅ Áudio como array/buffer direto na tentativa ${attempt}:`, {
            size: audioBuffer.length,
            type: 'direct_bytes'
          });
        } else if (audioData && typeof audioData === 'object') {
          // audioData é um objeto com propriedades
          if (!audioData.data) {
            console.warn(`⚠️ Tentativa ${attempt}: audioData.data está undefined:`, {
              hasData: !!audioData.data,
              mimetype: audioData.mimetype,
              keys: Object.keys(audioData)
            });
            continue;
          }

          audioBuffer = Buffer.from(audioData.data, 'base64');
          mimeType = audioData.mimetype || 'audio/ogg';
          console.log(`✅ Áudio como objeto na tentativa ${attempt}:`, {
            size: audioBuffer.length,
            mimeType
          });
        } else {
          console.warn(`⚠️ Tentativa ${attempt}: audioData em formato inesperado:`, typeof audioData);
          continue;
        }

        if (audioBuffer.length === 0) {
          console.warn(`⚠️ Tentativa ${attempt}: Buffer vazio`);
          continue;
        }

        return { buffer: audioBuffer, mimeType };

      } catch (error) {
        console.error(`❌ Erro na tentativa ${attempt} de download:`, error);

        if (attempt === maxRetries) {
          throw error;
        }

        // Aguardar antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    return null;
  }

  /**
   * Processar mensagem de áudio automaticamente
   */
  public async processAudioMessage(message: any): Promise<boolean> {
    if (!this.isActive) {
      return false;
    }

    try {
      // Verificar se é uma mensagem de áudio
      if (message.type !== 'ptt' && message.type !== 'audio') {
        return false; // Não é áudio, ignorar
      }

      console.log('🎵 Áudio recebido para processamento automático:', {
        from: message.from,
        type: message.type,
        id: message.id,
        timestamp: new Date().toISOString()
      });

      // Enviar confirmação imediata
      await this.sendImmediateConfirmation(message.from);

      // Baixar áudio com retry
      const audioResult = await this.downloadAudioWithRetry(message);
      if (!audioResult) {
        console.error('❌ Não foi possível baixar o áudio após múltiplas tentativas');
        await this.sendErrorMessage(message.from, message.id);
        return false;
      }

      const { buffer: audioBuffer, mimeType } = audioResult;

      // Validar tipo de áudio
      if (!WhatsAppAudioHandler.isValidAudioType(mimeType)) {
        console.warn('⚠️ Tipo de áudio não suportado:', mimeType);
        await this.sendUnsupportedTypeMessage(message.from);
        return false;
      }

      // Adicionar à fila de processamento
      const jobId = await audioQueue.addAudioJob(
        message.from,
        audioBuffer,
        mimeType,
        message.id,
        'normal' // Prioridade será ajustada automaticamente baseada no contato
      );

      console.log(`📥 Áudio adicionado à fila: ${jobId}`);

      // Configurar timeout para enviar status se demorar muito
      this.setupProcessingTimeout(message.from, jobId);

      return true;

    } catch (error) {
      console.error('❌ Erro ao processar áudio automaticamente:', error);
      await this.sendErrorMessage(message.from, message.id);
      return false;
    }
  }

  /**
   * Enviar confirmação imediata de recebimento
   */
  private async sendImmediateConfirmation(phoneNumber: string): Promise<void> {
    try {
      const confirmationMessages = [
        '🎵 Recebi seu áudio! Estou ouvindo e preparando uma resposta...',
        '🎤 Áudio recebido! Aguarde um momento enquanto analiso sua mensagem...',
        '🧡 Oi! Recebi sua mensagem de voz. Já estou preparando uma resposta carinhosa...'
      ];

      const randomMessage = confirmationMessages[Math.floor(Math.random() * confirmationMessages.length)];
      await this.client.sendText(phoneNumber, randomMessage);

      console.log(`✅ Confirmação enviada para ${phoneNumber}`);
    } catch (error) {
      console.error('❌ Erro ao enviar confirmação:', error);
    }
  }

  /**
   * Configurar timeout para status de processamento
   */
  private setupProcessingTimeout(phoneNumber: string, jobId: string): void {
    const timeout = setTimeout(async () => {
      try {
        await this.client.sendText(
          phoneNumber,
          '⏳ Ainda estou processando seu áudio... Pode levar alguns segundos para uma resposta completa!'
        );
        console.log(`⏰ Status de processamento enviado para ${phoneNumber}`);
      } catch (error) {
        console.error('❌ Erro ao enviar status:', error);
      }
    }, 15000); // 15 segundos

    this.messageHandlers.set(jobId, timeout);
  }

  /**
   * Enviar resposta final via WhatsApp
   */
  private async sendWhatsAppResponse(phoneNumber: string, response: string, messageId: string): Promise<void> {
    try {
      // Limpar timeout se existir
      const timeout = this.messageHandlers.get(messageId);
      if (timeout) {
        clearTimeout(timeout);
        this.messageHandlers.delete(messageId);
      }

      await this.client.sendText(phoneNumber, response);
      console.log(`✅ Resposta automática enviada para ${phoneNumber}`);

    } catch (error) {
      console.error('❌ Erro ao enviar resposta automática:', error);
    }
  }

  /**
   * Enviar mensagem de erro
   */
  private async sendErrorMessage(phoneNumber: string, messageId: string): Promise<void> {
    try {
      const errorMessages = [
        'Desculpe, tive dificuldades para processar seu áudio. Pode tentar enviar novamente?',
        'Ops! Algo deu errado ao analisar sua mensagem. Tente novamente, por favor.',
        'Não consegui processar seu áudio agora. Pode repetir a mensagem?'
      ];

      const randomError = errorMessages[Math.floor(Math.random() * errorMessages.length)];
      await this.client.sendText(phoneNumber, randomError);

    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de erro:', error);
    }
  }

  /**
   * Enviar mensagem para tipo não suportado
   */
  private async sendUnsupportedTypeMessage(phoneNumber: string): Promise<void> {
    try {
      await this.client.sendText(
        phoneNumber,
        'Desculpe, esse formato de áudio não é suportado. Tente gravar uma mensagem de voz normal pelo WhatsApp.'
      );
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de tipo não suportado:', error);
    }
  }

  /**
   * Obter estatísticas do processamento
   */
  public getStats() {
    return {
      isActive: this.isActive,
      pendingTimeouts: this.messageHandlers.size,
      queueStats: audioQueue.getStats()
    };
  }
}

/**
 * Função utilitária para integrar com o wppconnect (versão simplificada)
 * Esta função deve ser chamada quando uma mensagem de áudio é recebida
 */
export async function handleWhatsAppAudioMessage(
  client: any, // Cliente do wppconnect
  message: any, // Mensagem do WhatsApp
  geminiService: GeminiAIService
): Promise<void> {
  // Usar o handler automático se disponível
  if (global.automaticWhatsAppHandler) {
    await global.automaticWhatsAppHandler.processAudioMessage(message);
    return;
  }

  // Fallback para processamento direto (compatibilidade)
  try {
    if (message.type !== 'ptt' && message.type !== 'audio') {
      return;
    }

    console.log('🎵 Processamento direto de áudio (fallback):', {
      from: message.from,
      type: message.type,
      id: message.id
    });

    const audioData = await client.downloadMedia(message);
    if (!audioData) {
      console.error('❌ Não foi possível baixar o áudio');
      return;
    }

    // Verificar se os dados do áudio existem
    if (!audioData.data) {
      console.error('❌ Dados do áudio estão undefined no fallback:', audioData);
      return;
    }

    const audioBuffer = Buffer.from(audioData.data, 'base64');
    const mimeType = audioData.mimetype || 'audio/ogg';

    if (!WhatsAppAudioHandler.isValidAudioType(mimeType)) {
      console.warn('⚠️ Tipo de áudio não suportado:', mimeType);
      return;
    }

    // Adicionar à fila mesmo no modo fallback
    await audioQueue.addAudioJob(
      message.from,
      audioBuffer,
      mimeType,
      message.id,
      'normal'
    );

  } catch (error) {
    console.error('❌ Erro no processamento fallback:', error);
  }
}

// Exportar instância singleton
export const whatsappAudioHandler = new WhatsAppAudioHandler(new GeminiAIService());
