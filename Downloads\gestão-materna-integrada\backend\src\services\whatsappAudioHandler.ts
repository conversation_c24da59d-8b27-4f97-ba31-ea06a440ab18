import { GeminiAIService } from './gemini';
import { contactService } from './contactService';
import { messageService } from './messageService';
import { emitMessageReceived } from '../middleware/eventCapture';
import { audioQueue } from './audioQueue';

/**
 * Serviço para processar mensagens de áudio recebidas via WhatsApp
 * Este serviço será integrado com o wppconnect para processar áudios automaticamente
 */
export class WhatsAppAudioHandler {
  private geminiService: GeminiAIService;

  constructor(geminiService: GeminiAIService) {
    this.geminiService = geminiService;
  }

  /**
   * Processa uma mensagem de áudio recebida via WhatsApp
   * @param phoneNumber - Número de telefone do remetente
   * @param audioBuffer - Buffer do arquivo de áudio
   * @param mimeType - Tipo MIME do áudio
   * @param messageId - ID da mensagem no WhatsApp
   */
  async processIncomingAudio(
    phoneNumber: string,
    audioBuffer: Buffer,
    mimeType: string,
    messageId: string
  ): Promise<{
    success: boolean;
    response?: string;
    error?: string;
  }> {
    try {
      console.log('🎵 Processando áudio recebido via WhatsApp:', {
        phoneNumber,
        mimeType,
        audioSize: audioBuffer.length,
        messageId
      });

      // 1. Buscar ou criar contato
      let contact = await this.findOrCreateContact(phoneNumber);
      
      // 2. Salvar mensagem de áudio no banco
      await messageService.create({
        contact_id: contact.id,
        content: '[ÁUDIO RECEBIDO VIA WHATSAPP]',
        type: 'audio',
        from_me: false,
        timestamp: new Date().toISOString(),
        message_id: messageId
      });

      // 3. Processar áudio com Gemini AI
      console.log('🤖 Enviando áudio para Gemini AI...');
      const aiResponse = await this.geminiService.generateResponse(
        contact,
        'Mensagem de áudio recebida via WhatsApp',
        { buffer: audioBuffer, mimetype: mimeType }
      );

      console.log('✅ Resposta da IA gerada:', aiResponse.response);

      // 4. Salvar resposta da IA no banco
      await messageService.create({
        contact_id: contact.id,
        content: aiResponse.response,
        type: 'text',
        from_me: true,
        timestamp: new Date().toISOString(),
        sentiment: aiResponse.sentiment?.type || 'neutral'
      });

      // 5. Emitir evento para notificações
      emitMessageReceived({
        contactId: contact.id,
        contactName: contact.name,
        messageType: 'audio',
        aiResponse: aiResponse.response,
        sentiment: aiResponse.sentiment?.type || 'neutral'
      });

      // 6. Atualizar última interação do contato
      await contactService.update(contact.id, {
        last_interaction: new Date().toISOString()
      });

      return {
        success: true,
        response: aiResponse.response
      };

    } catch (error: any) {
      console.error('❌ Erro ao processar áudio do WhatsApp:', error);
      
      return {
        success: false,
        error: error.message || 'Erro interno ao processar áudio'
      };
    }
  }

  /**
   * Busca um contato existente ou cria um novo baseado no número de telefone
   */
  private async findOrCreateContact(phoneNumber: string) {
    try {
      // Tentar encontrar contato existente
      const existingContacts = await contactService.findAll();
      const existingContact = existingContacts.find(
        contact => contact.phone === phoneNumber
      );

      if (existingContact) {
        console.log('📞 Contato existente encontrado:', existingContact.name);
        return existingContact;
      }

      // Criar novo contato se não existir
      console.log('👤 Criando novo contato para:', phoneNumber);
      const newContact = await contactService.create({
        name: `Contato ${phoneNumber}`,
        phone: phoneNumber,
        baby_gender: 'unknown',
        registration_status: 'unregistered',
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      console.log('✅ Novo contato criado:', newContact.id);
      return newContact;

    } catch (error) {
      console.error('❌ Erro ao buscar/criar contato:', error);
      throw error;
    }
  }

  /**
   * Valida se o arquivo de áudio é suportado
   */
  static isValidAudioType(mimeType: string): boolean {
    const supportedTypes = [
      'audio/mpeg',
      'audio/mp3',
      'audio/wav',
      'audio/ogg',
      'audio/webm',
      'audio/m4a',
      'audio/aac',
      'audio/opus', // WhatsApp usa opus
      'audio/x-opus+ogg' // Variação do opus
    ];

    console.log(`🔍 Validando tipo de áudio: ${mimeType}`);
    const isValid = supportedTypes.includes(mimeType.toLowerCase());
    console.log(`${isValid ? '✅' : '❌'} Tipo ${mimeType} ${isValid ? 'suportado' : 'não suportado'}`);

    return isValid;
  }

  /**
   * Converte áudio para formato suportado se necessário
   * (Implementação futura - pode usar ffmpeg ou similar)
   */
  static async convertAudioIfNeeded(
    audioBuffer: Buffer,
    mimeType: string
  ): Promise<{ buffer: Buffer; mimeType: string }> {
    // Por enquanto, retorna o áudio original
    // No futuro, pode implementar conversão usando ffmpeg
    return { buffer: audioBuffer, mimeType };
  }
}

/**
 * Sistema automático de processamento de áudios do WhatsApp
 * Usa fila em background para garantir processamento mesmo com alto volume
 */
export class AutomaticWhatsAppHandler {
  private client: any;
  private isActive: boolean = false;
  private messageHandlers: Map<string, NodeJS.Timeout> = new Map();

  constructor(client: any) {
    this.client = client;
    this.setupEventListeners();
  }

  /**
   * Configurar listeners automáticos
   */
  private setupEventListeners(): void {
    // Listener para respostas prontas da fila
    audioQueue.on('responseReady', async (data) => {
      await this.sendWhatsAppResponse(data.phoneNumber, data.response, data.messageId);
    });

    // Listener para falhas críticas
    audioQueue.on('jobFailed', async (job, result) => {
      await this.sendErrorMessage(job.phoneNumber, job.messageId);
    });

    console.log('🎵 Listeners automáticos do WhatsApp configurados');
  }

  /**
   * Ativar processamento automático
   */
  public activate(): void {
    this.isActive = true;
    console.log('✅ Processamento automático de áudios ativado');
  }

  /**
   * Desativar processamento automático
   */
  public deactivate(): void {
    this.isActive = false;
    console.log('🛑 Processamento automático de áudios desativado');
  }

  /**
   * Baixar áudio com múltiplas tentativas - VERSÃO CORRIGIDA
   */
  private async downloadAudioWithRetry(message: any, maxRetries: number = 3): Promise<{ buffer: Buffer; mimeType: string } | null> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📥 Tentativa ${attempt}/${maxRetries} de download do áudio...`);
        console.log(`🔍 Dados da mensagem:`, {
          id: message.id,
          type: message.type,
          hasMedia: !!message.media,
          from: message.from,
          timestamp: message.timestamp
        });

        // Método 1: Tentar downloadMedia primeiro
        let audioData = null;
        try {
          audioData = await this.client.downloadMedia(message);
          console.log(`📥 downloadMedia resultado:`, {
            type: typeof audioData,
            isNull: audioData === null,
            isUndefined: audioData === undefined,
            length: audioData?.length || 'N/A',
            keys: audioData && typeof audioData === 'object' ? Object.keys(audioData) : 'N/A'
          });
        } catch (downloadError) {
          console.warn(`⚠️ downloadMedia falhou na tentativa ${attempt}:`, downloadError.message);
        }

        // Método 2: Se downloadMedia falhou, tentar método alternativo
        if (!audioData || (typeof audioData === 'string' && audioData.length < 100)) {
          console.log(`🔄 Tentando método alternativo de download...`);

          try {
            // Usar decryptFile se disponível
            if (this.client.decryptFile && message.deprecatedMms3Url) {
              audioData = await this.client.decryptFile(message);
              console.log(`📥 decryptFile resultado:`, {
                type: typeof audioData,
                length: audioData?.length || 'N/A'
              });
            }

            // Ou tentar getMessageMedia
            if (!audioData && this.client.getMessageMedia) {
              audioData = await this.client.getMessageMedia(message.id);
              console.log(`📥 getMessageMedia resultado:`, {
                type: typeof audioData,
                length: audioData?.length || 'N/A'
              });
            }
          } catch (altError) {
            console.warn(`⚠️ Método alternativo falhou:`, altError.message);
          }
        }

        if (!audioData) {
          console.warn(`⚠️ Tentativa ${attempt}: Nenhum método de download funcionou`);
          continue;
        }

        // Processar dados do áudio
        let audioBuffer: Buffer;
        let mimeType: string = 'audio/ogg'; // Padrão WhatsApp

        if (typeof audioData === 'string') {
          console.log(`🔍 String recebida, tamanho: ${audioData.length}`);

          if (audioData.length < 100) {
            console.warn(`⚠️ String muito pequena (${audioData.length} chars), provavelmente inválida`);
            continue;
          }

          // Tentar como base64
          try {
            audioBuffer = Buffer.from(audioData, 'base64');
            console.log(`✅ Convertido de base64: ${audioBuffer.length} bytes`);
          } catch (error) {
            console.warn(`⚠️ Erro ao converter base64, tentando como buffer direto`);
            audioBuffer = Buffer.from(audioData);
          }
        } else if (Buffer.isBuffer(audioData)) {
          audioBuffer = audioData;
          console.log(`✅ Buffer direto: ${audioBuffer.length} bytes`);
        } else if (Array.isArray(audioData)) {
          audioBuffer = Buffer.from(audioData);
          console.log(`✅ Array convertido: ${audioBuffer.length} bytes`);
        } else if (audioData && typeof audioData === 'object') {
          if (audioData.data) {
            audioBuffer = Buffer.from(audioData.data, 'base64');
            mimeType = audioData.mimetype || mimeType;
            console.log(`✅ Objeto com data: ${audioBuffer.length} bytes, tipo: ${mimeType}`);
          } else {
            console.warn(`⚠️ Objeto sem propriedade 'data':`, Object.keys(audioData));
            continue;
          }
        } else {
          console.warn(`⚠️ Formato inesperado:`, typeof audioData);
          continue;
        }

        // Validar tamanho mínimo
        if (audioBuffer.length < 1000) { // Áudio deve ter pelo menos 1KB
          console.warn(`⚠️ Áudio muito pequeno: ${audioBuffer.length} bytes`);
          continue;
        }

        console.log(`✅ Áudio baixado com sucesso na tentativa ${attempt}: ${audioBuffer.length} bytes`);
        return { buffer: audioBuffer, mimeType };

      } catch (error) {
        console.error(`❌ Erro na tentativa ${attempt} de download:`, error);

        if (attempt === maxRetries) {
          throw error;
        }

        // Aguardar antes da próxima tentativa
        await new Promise(resolve => setTimeout(resolve, 2000 * attempt));
      }
    }

    return null;
  }

  /**
   * Processar mensagem de áudio automaticamente
   */
  public async processAudioMessage(message: any): Promise<boolean> {
    if (!this.isActive) {
      return false;
    }

    try {
      // Verificar se é uma mensagem de áudio
      if (message.type !== 'ptt' && message.type !== 'audio' && message.type !== 'voice') {
        return false; // Não é áudio, ignorar
      }

      console.log('🎵 Áudio recebido para processamento automático:', {
        from: message.from,
        type: message.type,
        id: message.id,
        timestamp: new Date().toISOString(),
        hasMedia: !!message.media,
        hasBody: !!message.body,
        mimetype: message.mimetype,
        size: message.size
      });

      // Enviar confirmação imediata
      await this.sendImmediateConfirmation(message.from);

      // Baixar áudio com retry
      const audioResult = await this.downloadAudioWithRetry(message);
      if (!audioResult) {
        console.error('❌ Não foi possível baixar o áudio após múltiplas tentativas');
        await this.sendErrorMessage(message.from, message.id);
        return false;
      }

      const { buffer: audioBuffer, mimeType } = audioResult;

      // Validar tipo de áudio
      if (!WhatsAppAudioHandler.isValidAudioType(mimeType)) {
        console.warn('⚠️ Tipo de áudio não suportado:', mimeType);
        await this.sendUnsupportedTypeMessage(message.from);
        return false;
      }

      // Adicionar à fila de processamento
      const jobId = await audioQueue.addAudioJob(
        message.from,
        audioBuffer,
        mimeType,
        message.id,
        'normal' // Prioridade será ajustada automaticamente baseada no contato
      );

      console.log(`📥 Áudio adicionado à fila: ${jobId}`);

      // Configurar timeout para enviar status se demorar muito
      this.setupProcessingTimeout(message.from, jobId);

      return true;

    } catch (error) {
      console.error('❌ Erro ao processar áudio automaticamente:', error);
      await this.sendErrorMessage(message.from, message.id);
      return false;
    }
  }

  /**
   * Enviar confirmação imediata de recebimento
   */
  private async sendImmediateConfirmation(phoneNumber: string): Promise<void> {
    try {
      const confirmationMessages = [
        '🎵 Recebi seu áudio! Estou ouvindo e preparando uma resposta...',
        '🎤 Áudio recebido! Aguarde um momento enquanto analiso sua mensagem...',
        '🧡 Oi! Recebi sua mensagem de voz. Já estou preparando uma resposta carinhosa...'
      ];

      const randomMessage = confirmationMessages[Math.floor(Math.random() * confirmationMessages.length)];
      await this.client.sendText(phoneNumber, randomMessage);

      console.log(`✅ Confirmação enviada para ${phoneNumber}`);
    } catch (error) {
      console.error('❌ Erro ao enviar confirmação:', error);
    }
  }

  /**
   * Configurar timeout para status de processamento
   */
  private setupProcessingTimeout(phoneNumber: string, jobId: string): void {
    const timeout = setTimeout(async () => {
      try {
        await this.client.sendText(
          phoneNumber,
          '⏳ Ainda estou processando seu áudio... Pode levar alguns segundos para uma resposta completa!'
        );
        console.log(`⏰ Status de processamento enviado para ${phoneNumber}`);
      } catch (error) {
        console.error('❌ Erro ao enviar status:', error);
      }
    }, 15000); // 15 segundos

    this.messageHandlers.set(jobId, timeout);
  }

  /**
   * Enviar resposta final via WhatsApp
   */
  private async sendWhatsAppResponse(phoneNumber: string, response: string, messageId: string): Promise<void> {
    try {
      // Limpar timeout se existir
      const timeout = this.messageHandlers.get(messageId);
      if (timeout) {
        clearTimeout(timeout);
        this.messageHandlers.delete(messageId);
      }

      await this.client.sendText(phoneNumber, response);
      console.log(`✅ Resposta automática enviada para ${phoneNumber}`);

    } catch (error) {
      console.error('❌ Erro ao enviar resposta automática:', error);
    }
  }

  /**
   * Enviar mensagem de erro
   */
  private async sendErrorMessage(phoneNumber: string, messageId: string): Promise<void> {
    try {
      const errorMessages = [
        'Desculpe, tive dificuldades para processar seu áudio. Pode tentar enviar novamente?',
        'Ops! Algo deu errado ao analisar sua mensagem. Tente novamente, por favor.',
        'Não consegui processar seu áudio agora. Pode repetir a mensagem?'
      ];

      const randomError = errorMessages[Math.floor(Math.random() * errorMessages.length)];
      await this.client.sendText(phoneNumber, randomError);

    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de erro:', error);
    }
  }

  /**
   * Enviar mensagem para tipo não suportado
   */
  private async sendUnsupportedTypeMessage(phoneNumber: string): Promise<void> {
    try {
      await this.client.sendText(
        phoneNumber,
        'Desculpe, esse formato de áudio não é suportado. Tente gravar uma mensagem de voz normal pelo WhatsApp.'
      );
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem de tipo não suportado:', error);
    }
  }

  /**
   * Obter estatísticas do processamento
   */
  public getStats() {
    return {
      isActive: this.isActive,
      pendingTimeouts: this.messageHandlers.size,
      queueStats: audioQueue.getStats()
    };
  }
}

/**
 * Função utilitária para integrar com o wppconnect (versão simplificada)
 * Esta função deve ser chamada quando uma mensagem de áudio é recebida
 */
export async function handleWhatsAppAudioMessage(
  client: any, // Cliente do wppconnect
  message: any, // Mensagem do WhatsApp
  geminiService: GeminiAIService
): Promise<void> {
  // Usar o handler automático se disponível
  if (global.automaticWhatsAppHandler) {
    await global.automaticWhatsAppHandler.processAudioMessage(message);
    return;
  }

  // Fallback para processamento direto (compatibilidade)
  try {
    if (message.type !== 'ptt' && message.type !== 'audio') {
      return;
    }

    console.log('🎵 Processamento direto de áudio (fallback):', {
      from: message.from,
      type: message.type,
      id: message.id
    });

    const audioData = await client.downloadMedia(message);
    if (!audioData) {
      console.error('❌ Não foi possível baixar o áudio');
      return;
    }

    // Verificar se os dados do áudio existem
    if (!audioData.data) {
      console.error('❌ Dados do áudio estão undefined no fallback:', audioData);
      return;
    }

    const audioBuffer = Buffer.from(audioData.data, 'base64');
    const mimeType = audioData.mimetype || 'audio/ogg';

    if (!WhatsAppAudioHandler.isValidAudioType(mimeType)) {
      console.warn('⚠️ Tipo de áudio não suportado:', mimeType);
      return;
    }

    // Adicionar à fila mesmo no modo fallback
    await audioQueue.addAudioJob(
      message.from,
      audioBuffer,
      mimeType,
      message.id,
      'normal'
    );

  } catch (error) {
    console.error('❌ Erro no processamento fallback:', error);
  }
}

// Exportar instância singleton
export const whatsappAudioHandler = new WhatsAppAudioHandler(new GeminiAIService());
