"use strict";
// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use contactService do Supabase em vez deste modelo
Object.defineProperty(exports, "__esModule", { value: true });
exports.Contact = void 0;
// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use contactService do Supabase para todas as operações
// MODELO REMOVIDO - Use contactService do Supabase
exports.Contact = {
    find: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    findById: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    findOne: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    create: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    countDocuments: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    aggregate: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    updateMany: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    deleteMany: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    findByIdAndUpdate: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); },
    findByIdAndDelete: () => { throw new Error('MongoDB removido - Use contactService do Supabase'); }
};
