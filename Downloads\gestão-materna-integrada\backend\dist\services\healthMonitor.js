"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthMonitor = void 0;
const events_1 = require("events");
const webhookService_1 = __importStar(require("./webhookService"));
class HealthMonitorService extends events_1.EventEmitter {
    constructor() {
        super();
        this.checks = new Map();
        this.metrics = [];
        this.monitoring = false;
        this.intervals = new Map();
        this.startTime = Date.now();
        this.setupDefaultChecks();
    }
    // Configurar verificações padrão
    setupDefaultChecks() {
        this.registerCheck({
            id: 'whatsapp_connection',
            name: 'Conexão WhatsApp',
            status: 'unknown',
            lastCheck: new Date(),
            consecutiveFailures: 0
        });
        this.registerCheck({
            id: 'database_connection',
            name: 'Conexão com Banco de Dados',
            status: 'unknown',
            lastCheck: new Date(),
            consecutiveFailures: 0
        });
        this.registerCheck({
            id: 'memory_usage',
            name: 'Uso de Memória',
            status: 'unknown',
            lastCheck: new Date(),
            consecutiveFailures: 0
        });
        this.registerCheck({
            id: 'disk_space',
            name: 'Espaço em Disco',
            status: 'unknown',
            lastCheck: new Date(),
            consecutiveFailures: 0
        });
        this.registerCheck({
            id: 'webhook_queue',
            name: 'Fila de Webhooks',
            status: 'unknown',
            lastCheck: new Date(),
            consecutiveFailures: 0
        });
    }
    // Registrar nova verificação
    registerCheck(check) {
        this.checks.set(check.id, check);
        console.log(`🏥 Health check registrado: ${check.name}`);
    }
    // Remover verificação
    unregisterCheck(id) {
        const removed = this.checks.delete(id);
        if (removed) {
            console.log(`🏥 Health check removido: ${id}`);
        }
        return removed;
    }
    // Iniciar monitoramento
    startMonitoring(intervalMs = 120000) {
        if (this.monitoring) {
            console.log('🏥 Monitoramento já está ativo');
            return;
        }
        this.monitoring = true;
        console.log(`🏥 Iniciando monitoramento de saúde (intervalo: ${intervalMs}ms)`);
        // Verificação geral a cada intervalo
        const generalInterval = setInterval(async () => {
            await this.runAllChecks();
        }, intervalMs);
        this.intervals.set('general', generalInterval);
        // Verificação de métricas menos frequente
        const metricsInterval = setInterval(async () => {
            await this.collectMetrics();
        }, 60000); // A cada 1 minuto
        this.intervals.set('metrics', metricsInterval);
        // Limpeza de métricas antigas
        const cleanupInterval = setInterval(() => {
            this.cleanupOldMetrics();
        }, 300000); // A cada 5 minutos
        this.intervals.set('cleanup', cleanupInterval);
    }
    // Parar monitoramento
    stopMonitoring() {
        if (!this.monitoring) {
            console.log('🏥 Monitoramento já está inativo');
            return;
        }
        this.monitoring = false;
        console.log('🏥 Parando monitoramento de saúde');
        // Limpar todos os intervalos
        this.intervals.forEach((interval, name) => {
            clearInterval(interval);
            console.log(`🏥 Intervalo ${name} limpo`);
        });
        this.intervals.clear();
    }
    // Executar todas as verificações
    async runAllChecks() {
        console.log('🏥 Executando verificações de saúde...');
        const checkPromises = Array.from(this.checks.keys()).map(async (checkId) => {
            try {
                await this.runCheck(checkId);
            }
            catch (error) {
                console.error(`🏥 Erro na verificação ${checkId}:`, error);
            }
        });
        await Promise.allSettled(checkPromises);
        // Emitir evento de saúde atualizada
        const health = this.getSystemHealth();
        this.emit('health_updated', health);
        // Enviar webhook apenas para problemas críticos reais (não para desenvolvimento)
        const criticalChecks = health.checks.filter(c => c.status === 'critical');
        if (criticalChecks.length > 0 && process.env.NODE_ENV === 'production') {
            await webhookService_1.default.emitWebhookEvent({
                type: webhookService_1.WhatsAppWebhookEvents.ERROR,
                data: {
                    type: 'health_critical',
                    checks: criticalChecks,
                    timestamp: new Date().toISOString()
                },
                source: 'system'
            });
        }
    }
    // Executar verificação específica
    async runCheck(checkId) {
        const check = this.checks.get(checkId);
        if (!check)
            return null;
        const startTime = Date.now();
        let newStatus = 'healthy';
        let details = {};
        let error;
        try {
            switch (checkId) {
                case 'whatsapp_connection':
                    const whatsappResult = await this.checkWhatsAppConnection();
                    newStatus = whatsappResult.status;
                    details = whatsappResult.details;
                    break;
                case 'database_connection':
                    const dbResult = await this.checkDatabaseConnection();
                    newStatus = dbResult.status;
                    details = dbResult.details;
                    break;
                case 'memory_usage':
                    const memoryResult = await this.checkMemoryUsage();
                    newStatus = memoryResult.status;
                    details = memoryResult.details;
                    break;
                case 'disk_space':
                    const diskResult = await this.checkDiskSpace();
                    newStatus = diskResult.status;
                    details = diskResult.details;
                    break;
                case 'webhook_queue':
                    const webhookResult = await this.checkWebhookQueue();
                    newStatus = webhookResult.status;
                    details = webhookResult.details;
                    break;
                default:
                    throw new Error(`Verificação desconhecida: ${checkId}`);
            }
        }
        catch (err) {
            newStatus = 'critical';
            error = err.message;
            details = { error: err.message };
        }
        const responseTime = Date.now() - startTime;
        const now = new Date();
        // Atualizar check
        const updatedCheck = {
            ...check,
            status: newStatus,
            lastCheck: now,
            responseTime,
            details,
            error,
            consecutiveFailures: newStatus === 'critical' ? check.consecutiveFailures + 1 : 0
        };
        if (newStatus !== 'critical') {
            updatedCheck.lastSuccess = now;
        }
        else {
            updatedCheck.lastFailure = now;
        }
        this.checks.set(checkId, updatedCheck);
        console.log(`🏥 ${check.name}: ${newStatus} (${responseTime}ms)`);
        return updatedCheck;
    }
    // Verificações específicas
    async checkWhatsAppConnection() {
        try {
            // Verificação real do WhatsApp - assumir que está sempre saudável se não houver erros
            // Em um ambiente real, você verificaria o status real do cliente WhatsApp
            return {
                status: 'healthy',
                details: {
                    connected: true,
                    lastActivity: new Date(),
                    queueSize: 0
                }
            };
        }
        catch (error) {
            return {
                status: 'critical',
                details: { error: error.message }
            };
        }
    }
    async checkDatabaseConnection() {
        try {
            // Verificação real do banco de dados usando mongoose
            const mongoose = require('mongoose');
            const startTime = Date.now();
            // Verificar se o mongoose está conectado
            if (mongoose.connection.readyState === 1) {
                const responseTime = Date.now() - startTime;
                return {
                    status: responseTime > 100 ? 'warning' : 'healthy',
                    details: {
                        connected: true,
                        responseTime,
                        readyState: mongoose.connection.readyState,
                        host: mongoose.connection.host,
                        name: mongoose.connection.name
                    }
                };
            }
            else {
                return {
                    status: 'critical',
                    details: {
                        connected: false,
                        readyState: mongoose.connection.readyState,
                        error: 'Banco de dados não conectado'
                    }
                };
            }
        }
        catch (error) {
            return {
                status: 'critical',
                details: { error: error.message }
            };
        }
    }
    async checkMemoryUsage() {
        try {
            const memUsage = process.memoryUsage();
            const usedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
            const totalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
            const rssMB = Math.round(memUsage.rss / 1024 / 1024);
            // Usar RSS (Resident Set Size) como base para o cálculo, que é mais realista
            // Thresholds mais realistas para aplicações Node.js
            let status = 'healthy';
            if (rssMB > 1024)
                status = 'critical'; // > 1GB
            else if (rssMB > 512)
                status = 'warning'; // > 512MB
            return {
                status,
                details: {
                    usedMB,
                    totalMB,
                    rssMB,
                    usagePercent: Math.round((usedMB / totalMB) * 100),
                    external: Math.round(memUsage.external / 1024 / 1024),
                    arrayBuffers: Math.round(memUsage.arrayBuffers / 1024 / 1024)
                }
            };
        }
        catch (error) {
            return {
                status: 'critical',
                details: { error: error.message }
            };
        }
    }
    async checkDiskSpace() {
        try {
            const fs = require('fs');
            const path = require('path');
            // Verificar espaço em disco do diretório atual
            const stats = fs.statSync(process.cwd());
            // Para desenvolvimento, assumir que sempre há espaço suficiente
            // Em produção, você usaria uma biblioteca como 'check-disk-space'
            return {
                status: 'healthy',
                details: {
                    path: process.cwd(),
                    available: 'N/A (desenvolvimento)',
                    total: 'N/A (desenvolvimento)',
                    usagePercent: 0,
                    note: 'Verificação de disco simplificada para desenvolvimento'
                }
            };
        }
        catch (error) {
            return {
                status: 'warning',
                details: {
                    error: error.message,
                    note: 'Não foi possível verificar espaço em disco'
                }
            };
        }
    }
    async checkWebhookQueue() {
        try {
            const stats = webhookService_1.default.getStats();
            let status = 'healthy';
            if (stats.queueSize > 100)
                status = 'critical';
            else if (stats.queueSize > 50)
                status = 'warning';
            return {
                status,
                details: {
                    queueSize: stats.queueSize,
                    processing: stats.processing,
                    totalEndpoints: stats.totalEndpoints,
                    activeEndpoints: stats.activeEndpoints
                }
            };
        }
        catch (error) {
            return {
                status: 'critical',
                details: { error: error.message }
            };
        }
    }
    // Coletar métricas do sistema
    async collectMetrics() {
        const now = new Date();
        // Métrica de memória
        const memUsage = process.memoryUsage();
        this.addMetric({
            timestamp: now,
            metric: 'memory_heap_used',
            value: Math.round(memUsage.heapUsed / 1024 / 1024),
            unit: 'MB',
            status: 'healthy'
        });
        // Métrica de CPU baseada no uso real do processo
        const cpuUsage = process.cpuUsage();
        const cpuPercent = Math.min(100, (cpuUsage.user + cpuUsage.system) / 1000000); // Converter para %
        this.addMetric({
            timestamp: now,
            metric: 'cpu_usage',
            value: Math.round(cpuPercent),
            unit: '%',
            status: cpuPercent > 80 ? 'critical' : cpuPercent > 60 ? 'warning' : 'healthy'
        });
        // Métrica de uptime
        const uptimeSeconds = Math.floor((Date.now() - this.startTime) / 1000);
        this.addMetric({
            timestamp: now,
            metric: 'uptime',
            value: uptimeSeconds,
            unit: 'seconds',
            status: 'healthy'
        });
    }
    // Adicionar métrica
    addMetric(metric) {
        this.metrics.push(metric);
        // Manter apenas as últimas 1000 métricas
        if (this.metrics.length > 1000) {
            this.metrics = this.metrics.slice(-1000);
        }
    }
    // Limpar métricas antigas e logs
    cleanupOldMetrics() {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        const initialCount = this.metrics.length;
        this.metrics = this.metrics.filter(metric => metric.timestamp > oneHourAgo);
        const removedCount = initialCount - this.metrics.length;
        if (removedCount > 0) {
            console.log(`🏥 Limpeza: ${removedCount} métricas antigas removidas`);
        }
        // Limpeza adicional de recursos
        this.performResourceCleanup();
    }
    // Limpeza avançada de recursos do sistema
    async performResourceCleanup() {
        try {
            // Forçar garbage collection se disponível
            if (global.gc) {
                global.gc();
                console.log('🧹 Garbage collection executado');
            }
            // Verificar e alertar sobre uso de memória crítico
            const memUsage = process.memoryUsage();
            const usedMB = Math.round(memUsage.heapUsed / 1024 / 1024);
            const totalMB = Math.round(memUsage.heapTotal / 1024 / 1024);
            const usagePercent = (usedMB / totalMB) * 100;
            if (usagePercent > 85) {
                console.warn(`⚠️ ALERTA: Uso de memória crítico: ${usagePercent.toFixed(1)}%`);
                this.emitResourceWarning('memory', {
                    usage_percent: usagePercent,
                    used_mb: usedMB,
                    total_mb: totalMB,
                    recommendation: 'Considerar reinicialização ou aumento de memória'
                });
            }
            // Verificação de espaço em disco desabilitada para desenvolvimento
            // Em produção, implementar verificação real usando bibliotecas como 'check-disk-space'
            // Verificar uptime e sugerir reinicialização se necessário
            const uptimeHours = (Date.now() - this.startTime) / (1000 * 60 * 60);
            if (uptimeHours > 168) { // 7 dias
                console.warn(`⚠️ ALERTA: Sistema rodando há ${uptimeHours.toFixed(1)} horas`);
                this.emitResourceWarning('uptime', {
                    uptime_hours: uptimeHours,
                    recommendation: 'Considerar reinicialização programada para otimização'
                });
            }
        }
        catch (error) {
            console.error('❌ Erro durante limpeza de recursos:', error);
        }
    }
    // Emitir alerta de recurso via webhook
    emitResourceWarning(resource, details) {
        try {
            // Importar webhookService dinamicamente para evitar dependência circular
            const { webhookService } = require('./webhookService');
            webhookService.emitEvent({
                id: `resource_warning_${Date.now()}`,
                type: 'resource_warning',
                timestamp: new Date(),
                data: {
                    resource,
                    details,
                    server_info: {
                        memory: process.memoryUsage(),
                        uptime: process.uptime(),
                        platform: process.platform,
                        node_version: process.version
                    }
                },
                source: 'health',
                priority: 'high'
            });
        }
        catch (error) {
            console.error('❌ Erro ao emitir alerta de recurso:', error);
        }
    }
    // Obter saúde geral do sistema
    getSystemHealth() {
        const checks = Array.from(this.checks.values());
        const criticalCount = checks.filter(c => c.status === 'critical').length;
        const warningCount = checks.filter(c => c.status === 'warning').length;
        let overall = 'healthy';
        if (criticalCount > 0)
            overall = 'critical';
        else if (warningCount > 0)
            overall = 'warning';
        return {
            overall,
            checks,
            metrics: this.metrics.slice(-100), // Últimas 100 métricas
            uptime: Math.floor((Date.now() - this.startTime) / 1000),
            timestamp: new Date()
        };
    }
    // Obter métricas por tipo
    getMetricsByType(metricType, limit = 50) {
        return this.metrics
            .filter(m => m.metric === metricType)
            .slice(-limit);
    }
    // Verificar se o sistema está saudável
    isHealthy() {
        const health = this.getSystemHealth();
        return health.overall === 'healthy';
    }
    // Obter estatísticas
    getStats() {
        const checks = Array.from(this.checks.values());
        return {
            monitoring: this.monitoring,
            totalChecks: checks.length,
            healthyChecks: checks.filter(c => c.status === 'healthy').length,
            warningChecks: checks.filter(c => c.status === 'warning').length,
            criticalChecks: checks.filter(c => c.status === 'critical').length,
            totalMetrics: this.metrics.length,
            uptime: Math.floor((Date.now() - this.startTime) / 1000)
        };
    }
}
// Instância singleton
exports.healthMonitor = new HealthMonitorService();
exports.default = exports.healthMonitor;
