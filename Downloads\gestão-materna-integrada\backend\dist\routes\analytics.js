"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// GET /api/analytics/dashboard - Dashboard principal
router.get('/dashboard', 
// authenticate, // Removido temporariamente para teste
// authorize('read:analytics'), // Removido temporariamente para teste
[
    (0, express_validator_1.query)('period').optional().isIn(['today', 'week', 'month', 'quarter', 'year']),
    (0, express_validator_1.query)('startDate').optional().isISO8601(),
    (0, express_validator_1.query)('endDate').optional().isISO8601()
], async (req, res) => {
    try {
        console.log('📊 Analytics do dashboard solicitado (sem auth)');
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                error: 'Parâmetros inválidos',
                code: 'VALIDATION_ERROR',
                details: errors.array()
            });
        }
        const period = req.query.period || 'month';
        let startDate;
        let endDate = new Date();
        // Definir período
        switch (period) {
            case 'today':
                startDate = new Date();
                startDate.setHours(0, 0, 0, 0);
                break;
            case 'week':
                startDate = new Date();
                startDate.setDate(startDate.getDate() - 7);
                break;
            case 'month':
                startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
                break;
            case 'quarter':
                startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 3);
                break;
            case 'year':
                startDate = new Date();
                startDate.setFullYear(startDate.getFullYear() - 1);
                break;
            default:
                startDate = req.query.startDate ? new Date(req.query.startDate) : new Date();
                endDate = req.query.endDate ? new Date(req.query.endDate) : new Date();
        }
        // Filtros por usuário (removido temporariamente para teste)
        const userFilter = {};
        // Estatísticas de contatos
        const contactStats = await Contact_1.Contact.aggregate([
            { $match: { isActive: true, ...userFilter } },
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    highRisk: { $sum: { $cond: ['$isHighRisk', 1, 0] } },
                    urgent: { $sum: { $cond: [{ $eq: ['$priority', 'urgent'] }, 1, 0] } },
                    firstTrimester: { $sum: { $cond: [{ $eq: ['$pregnancyStage', 'first_trimester'] }, 1, 0] } },
                    secondTrimester: { $sum: { $cond: [{ $eq: ['$pregnancyStage', 'second_trimester'] }, 1, 0] } },
                    thirdTrimester: { $sum: { $cond: [{ $eq: ['$pregnancyStage', 'third_trimester'] }, 1, 0] } },
                    postpartum: { $sum: { $cond: [{ $eq: ['$pregnancyStage', 'postpartum'] }, 1, 0] } }
                }
            }
        ]);
        // Contatos com parto próximo (próximas 2 semanas)
        const twoWeeksFromNow = new Date();
        twoWeeksFromNow.setDate(twoWeeksFromNow.getDate() + 14);
        const dueSoon = await Contact_1.Contact.countDocuments({
            ...userFilter,
            isActive: true,
            dueDate: { $lte: twoWeeksFromNow, $gte: new Date() }
        });
        // Contatos atrasados
        const overdue = await Contact_1.Contact.countDocuments({
            ...userFilter,
            isActive: true,
            dueDate: { $lt: new Date() }
        });
        // Estatísticas de mensagens no período
        const messageStats = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: startDate, $lte: endDate }
                }
            },
            {
                $group: {
                    _id: null,
                    total: { $sum: 1 },
                    fromMe: { $sum: { $cond: ['$fromMe', 1, 0] } },
                    fromContacts: { $sum: { $cond: ['$fromMe', 0, 1] } },
                    urgent: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'urgent'] }, 1, 0] } },
                    negative: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'negative'] }, 1, 0] } },
                    positive: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'positive'] }, 1, 0] } },
                    unprocessed: { $sum: { $cond: [{ $eq: ['$processedBy', null] }, 1, 0] } }
                }
            }
        ]);
        // Distribuição por categoria
        const categoryStats = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: startDate, $lte: endDate },
                    category: { $exists: true }
                }
            },
            {
                $group: {
                    _id: '$category',
                    count: { $sum: 1 }
                }
            },
            { $sort: { count: -1 } }
        ]);
        // Atividade por dia (últimos 30 dias)
        const dailyActivity = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
                }
            },
            {
                $group: {
                    _id: {
                        year: { $year: '$timestamp' },
                        month: { $month: '$timestamp' },
                        day: { $dayOfMonth: '$timestamp' }
                    },
                    messages: { $sum: 1 },
                    fromMe: { $sum: { $cond: ['$fromMe', 1, 0] } },
                    fromContacts: { $sum: { $cond: ['$fromMe', 0, 1] } }
                }
            },
            { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
        ]);
        // Top 5 contatos mais ativos
        const topContacts = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: startDate, $lte: endDate },
                    fromMe: false
                }
            },
            {
                $group: {
                    _id: '$contact',
                    messageCount: { $sum: 1 },
                    lastMessage: { $max: '$timestamp' }
                }
            },
            { $sort: { messageCount: -1 } },
            { $limit: 5 },
            {
                $lookup: {
                    from: 'contacts',
                    localField: '_id',
                    foreignField: '_id',
                    as: 'contact'
                }
            },
            { $unwind: '$contact' },
            {
                $project: {
                    name: '$contact.name',
                    phone: '$contact.phone',
                    pregnancyStage: '$contact.pregnancyStage',
                    messageCount: 1,
                    lastMessage: 1
                }
            }
        ]);
        const result = {
            period: {
                type: period,
                startDate,
                endDate
            },
            contacts: {
                ...(contactStats[0] || {
                    total: 0,
                    highRisk: 0,
                    urgent: 0,
                    firstTrimester: 0,
                    secondTrimester: 0,
                    thirdTrimester: 0,
                    postpartum: 0
                }),
                dueSoon,
                overdue
            },
            messages: messageStats[0] || {
                total: 0,
                fromMe: 0,
                fromContacts: 0,
                urgent: 0,
                negative: 0,
                positive: 0,
                unprocessed: 0
            },
            categories: categoryStats,
            dailyActivity,
            topContacts
        };
        console.log('📊 Analytics calculado - Gestantes encontradas:', result.contacts.total);
        console.log('📊 Mensagens encontradas:', result.messages.total);
        console.log('📊 Atividade diária:', result.dailyActivity.length, 'dias');
        console.log('📊 Resultado completo sendo enviado:', JSON.stringify(result, null, 2));
        res.json(result);
    }
    catch (error) {
        console.error('Erro ao obter analytics do dashboard:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
// GET /api/analytics/messages - Analytics de mensagens
router.get('/messages', auth_1.authenticate, (0, auth_1.authorize)('read:analytics'), [
    (0, express_validator_1.query)('period').optional().isIn(['today', 'week', 'month', 'quarter', 'year']),
    (0, express_validator_1.query)('groupBy').optional().isIn(['hour', 'day', 'week', 'month'])
], async (req, res) => {
    try {
        const period = req.query.period || 'month';
        const groupBy = req.query.groupBy || 'day';
        let startDate;
        const endDate = new Date();
        // Definir período
        switch (period) {
            case 'today':
                startDate = new Date();
                startDate.setHours(0, 0, 0, 0);
                break;
            case 'week':
                startDate = new Date();
                startDate.setDate(startDate.getDate() - 7);
                break;
            case 'month':
                startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
                break;
            case 'quarter':
                startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 3);
                break;
            case 'year':
                startDate = new Date();
                startDate.setFullYear(startDate.getFullYear() - 1);
                break;
            default:
                startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 1);
        }
        // Configurar agrupamento
        let groupConfig;
        switch (groupBy) {
            case 'hour':
                groupConfig = {
                    year: { $year: '$timestamp' },
                    month: { $month: '$timestamp' },
                    day: { $dayOfMonth: '$timestamp' },
                    hour: { $hour: '$timestamp' }
                };
                break;
            case 'day':
                groupConfig = {
                    year: { $year: '$timestamp' },
                    month: { $month: '$timestamp' },
                    day: { $dayOfMonth: '$timestamp' }
                };
                break;
            case 'week':
                groupConfig = {
                    year: { $year: '$timestamp' },
                    week: { $week: '$timestamp' }
                };
                break;
            case 'month':
                groupConfig = {
                    year: { $year: '$timestamp' },
                    month: { $month: '$timestamp' }
                };
                break;
        }
        // Analytics de mensagens por período
        const messageAnalytics = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: startDate, $lte: endDate }
                }
            },
            {
                $group: {
                    _id: groupConfig,
                    total: { $sum: 1 },
                    fromMe: { $sum: { $cond: ['$fromMe', 1, 0] } },
                    fromContacts: { $sum: { $cond: ['$fromMe', 0, 1] } },
                    urgent: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'urgent'] }, 1, 0] } },
                    negative: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'negative'] }, 1, 0] } },
                    positive: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'positive'] }, 1, 0] } },
                    neutral: { $sum: { $cond: [{ $eq: ['$sentiment.type', 'neutral'] }, 1, 0] } }
                }
            },
            { $sort: { '_id': 1 } }
        ]);
        // Análise de sentimento geral
        const sentimentAnalysis = await Message_1.Message.aggregate([
            {
                $match: {
                    timestamp: { $gte: startDate, $lte: endDate },
                    'sentiment.type': { $exists: true }
                }
            },
            {
                $group: {
                    _id: '$sentiment.type',
                    count: { $sum: 1 },
                    avgScore: { $avg: '$sentiment.score' }
                }
            }
        ]);
        res.json({
            period: { startDate, endDate, groupBy },
            timeline: messageAnalytics,
            sentiment: sentimentAnalysis
        });
    }
    catch (error) {
        console.error('Erro ao obter analytics de mensagens:', error);
        res.status(500).json({
            error: 'Erro interno do servidor',
            code: 'INTERNAL_ERROR'
        });
    }
});
exports.default = router;
