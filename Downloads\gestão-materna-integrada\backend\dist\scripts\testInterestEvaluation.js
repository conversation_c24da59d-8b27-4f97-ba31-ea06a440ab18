"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testInterestEvaluation = testInterestEvaluation;
exports.testNeutralResponses = testNeutralResponses;
exports.testInterestAnalysis = testInterestAnalysis;
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("../config/database");
const Contact_1 = require("../models/Contact");
const gemini_1 = require("../services/gemini");
const interestEvaluator_1 = __importDefault(require("../services/interestEvaluator"));
dotenv_1.default.config();
/**
 * Script para testar o sistema de avaliação de interesse
 */
async function testInterestEvaluation() {
    console.log('🧪 Iniciando teste do sistema de avaliação de interesse...\n');
    try {
        // Conectar ao banco de dados
        await (0, database_1.connectDatabase)();
        console.log('✅ Conectado ao banco de dados\n');
        // Inicializar serviços
        const geminiService = new gemini_1.GeminiAIService();
        const interestEvaluator = new interestEvaluator_1.default(geminiService);
        // Cenários de teste
        const testScenarios = [
            {
                name: 'Pessoa claramente grávida',
                messages: [
                    'Olá, estou grávida de 3 meses',
                    'Meu nome é Maria Silva, telefone (11) 99999-9999, é menina'
                ]
            },
            {
                name: 'Pessoa com dúvidas sobre gravidez',
                messages: [
                    'Tenho dúvidas sobre pré-natal',
                    'Sim, estou grávida',
                    'Ana Costa, (11) 88888-8888, ainda não sei o sexo'
                ]
            },
            {
                name: 'Pessoa não relacionada ao projeto',
                messages: [
                    'Oi, tudo bem?',
                    'Estou procurando informações sobre trabalho'
                ]
            },
            {
                name: 'Mensagem genérica',
                messages: [
                    'Olá'
                ]
            }
        ];
        for (let i = 0; i < testScenarios.length; i++) {
            const scenario = testScenarios[i];
            console.log(`\n📋 Testando cenário ${i + 1}: ${scenario.name}`);
            console.log('='.repeat(50));
            // Criar contato de teste
            const testPhone = `+5511999${String(i).padStart(6, '0')}`;
            // Limpar contato existente se houver
            await Contact_1.Contact.deleteOne({ phone: testPhone });
            let contact = await Contact_1.Contact.create({
                phone: testPhone,
                name: `Teste ${i + 1}`,
                registrationStatus: 'unregistered',
                evaluationStartDate: new Date(),
                evaluationMessages: 0,
                interestScore: 0,
                isActive: true,
                lastInteraction: new Date()
            });
            console.log(`👤 Contato criado: ${contact.name} (${contact.phone})`);
            // Processar cada mensagem
            for (let j = 0; j < scenario.messages.length; j++) {
                const message = scenario.messages[j];
                console.log(`\n📨 Mensagem ${j + 1}: "${message}"`);
                if (contact.registrationStatus === 'unregistered') {
                    // Primeira mensagem - analisar se é sobre o projeto
                    const analysis = await interestEvaluator.analyzeIfRelatedToProject(message);
                    console.log(`📊 Análise: Relacionado=${analysis.isRelated}, Oferecer cadastro=${analysis.shouldOfferRegistration}, Confiança=${analysis.confidence}%`);
                    if (analysis.isRelated && analysis.shouldOfferRegistration) {
                        const registrationOffer = await interestEvaluator.generateRegistrationOffer(message);
                        console.log(`📤 Oferta de cadastro: "${registrationOffer}"`);
                        contact.registrationStatus = 'interested';
                    }
                    else if (analysis.isRelated && !analysis.shouldOfferRegistration) {
                        console.log(`📤 Pergunta de esclarecimento: "Você está grávida ou tem alguma dúvida sobre gravidez?"`);
                        contact.registrationStatus = 'evaluating';
                    }
                    else {
                        console.log(`🚫 Não relacionado - não respondendo (deixando para humano)`);
                        contact.registrationStatus = 'not_interested';
                        contact.isActive = false;
                    }
                    await contact.save();
                }
                else if (contact.registrationStatus === 'evaluating') {
                    // Segunda análise
                    const analysis = await interestEvaluator.analyzeIfRelatedToProject(message);
                    if (analysis.shouldOfferRegistration) {
                        const registrationOffer = await interestEvaluator.generateRegistrationOffer(message);
                        console.log(`📤 Cadastro oferecido: "${registrationOffer}"`);
                        contact.registrationStatus = 'interested';
                    }
                    else if (!analysis.isRelated) {
                        console.log(`🚫 Não relacionado - parando de responder`);
                        contact.registrationStatus = 'not_interested';
                        contact.isActive = false;
                    }
                    else {
                        console.log(`📤 Segunda pergunta: "Posso ajudá-la com alguma questão específica sobre gravidez?"`);
                    }
                    await contact.save();
                }
                else if (contact.registrationStatus === 'interested') {
                    // Processar dados de cadastro
                    const registrationResult = await interestEvaluator.processRegistrationData(contact, message);
                    console.log(`📝 Cadastro - Sucesso: ${registrationResult.success}, Precisa mais dados: ${registrationResult.needsMoreData}`);
                    console.log(`📤 Resposta: "${registrationResult.response}"`);
                    if (registrationResult.extractedData) {
                        console.log(`📋 Dados extraídos:`, registrationResult.extractedData);
                    }
                    // Recarregar contato
                    contact = await Contact_1.Contact.findById(contact._id) || contact;
                }
            }
            // Status final
            console.log(`\n✅ Status final: ${contact.registrationStatus}`);
            console.log(`📊 Score final: ${contact.interestScore}`);
            console.log(`📈 Mensagens de avaliação: ${contact.evaluationMessages}`);
        }
        console.log('\n🎉 Teste concluído com sucesso!');
        // Mostrar estatísticas finais
        console.log('\n📊 Estatísticas finais:');
        const stats = await Contact_1.Contact.aggregate([
            {
                $group: {
                    _id: '$registrationStatus',
                    count: { $sum: 1 },
                    avgScore: { $avg: '$interestScore' }
                }
            }
        ]);
        stats.forEach(stat => {
            var _a;
            console.log(`- ${stat._id}: ${stat.count} contatos (score médio: ${((_a = stat.avgScore) === null || _a === void 0 ? void 0 : _a.toFixed(1)) || 0})`);
        });
    }
    catch (error) {
        console.error('❌ Erro durante o teste:', error);
    }
}
/**
 * Testar apenas a geração de respostas neutras
 */
async function testNeutralResponses() {
    console.log('\n🧪 Testando respostas neutras...\n');
    const geminiService = new gemini_1.GeminiAIService();
    const interestEvaluator = new interestEvaluator_1.default(geminiService);
    const testMessages = [
        'Olá',
        'Oi, tudo bem?',
        'Estou grávida',
        'Preciso de ajuda',
        'Oi, foi engano',
        'Como funciona?'
    ];
    for (const message of testMessages) {
        try {
            const response = await interestEvaluator.generateNeutralResponse('+5511999999999', message);
            console.log(`📨 "${message}" → 📤 "${response}"`);
        }
        catch (error) {
            console.error(`❌ Erro para "${message}":`, error.message);
        }
    }
}
/**
 * Testar análise de interesse
 */
async function testInterestAnalysis() {
    console.log('\n🧪 Testando análise de interesse...\n');
    await (0, database_1.connectDatabase)();
    const geminiService = new gemini_1.GeminiAIService();
    const interestEvaluator = new interestEvaluator_1.default(geminiService);
    // Criar contato de teste
    const contact = await Contact_1.Contact.create({
        phone: '+5511999999999',
        name: 'Teste Análise',
        registrationStatus: 'evaluating',
        evaluationStartDate: new Date(),
        evaluationMessages: 0,
        interestScore: 0,
        isActive: true,
        lastInteraction: new Date()
    });
    const testMessages = [
        { message: 'Estou grávida de 3 meses', expectedScore: 'alto' },
        { message: 'Tenho dúvidas sobre pré-natal', expectedScore: 'alto' },
        { message: 'Gostaria de acompanhamento', expectedScore: 'alto' },
        { message: 'Ok', expectedScore: 'baixo' },
        { message: 'Não estou interessada', expectedScore: 'muito baixo' },
        { message: 'Foi engano, desculpa', expectedScore: 'muito baixo' }
    ];
    for (const test of testMessages) {
        try {
            const evaluation = await interestEvaluator.evaluateInterest(contact, test.message);
            console.log(`📨 "${test.message}"`);
            console.log(`📊 Score: ${evaluation.interestScore} (esperado: ${test.expectedScore})`);
            console.log(`🎯 Ação: ${evaluation.action}`);
            console.log(`📤 Resposta: "${evaluation.response}"`);
            console.log('---');
            // Recarregar contato para próxima iteração
            const updatedContact = await Contact_1.Contact.findById(contact._id);
            if (updatedContact) {
                contact.evaluationMessages = updatedContact.evaluationMessages;
                contact.interestScore = updatedContact.interestScore;
            }
        }
        catch (error) {
            console.error(`❌ Erro para "${test.message}":`, error.message);
        }
    }
    // Limpar contato de teste
    await Contact_1.Contact.findByIdAndDelete(contact._id);
}
// Executar testes baseado no argumento
const testType = process.argv[2] || 'full';
async function runTests() {
    switch (testType) {
        case 'neutral':
            await testNeutralResponses();
            break;
        case 'analysis':
            await testInterestAnalysis();
            break;
        case 'full':
        default:
            await testInterestEvaluation();
            break;
    }
    process.exit(0);
}
if (require.main === module) {
    runTests().catch(console.error);
}
