"use strict";
// MODELO REMOVIDO - MongoDB substituído por Supabase
// Use messageService do Supabase em vez deste modelo
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = void 0;
// TODOS OS SCHEMAS E MÉTODOS MONGODB FORAM REMOVIDOS
// Use messageService do Supabase para todas as operações
// MODELO REMOVIDO - Use messageService do Supabase
exports.Message = {
    find: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
    findById: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
    findOne: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
    create: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
    countDocuments: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); },
    aggregate: () => { throw new Error('MongoDB removido - Use messageService do Supabase'); }
};
