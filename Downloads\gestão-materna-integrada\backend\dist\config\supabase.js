"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testSupabaseConnection = exports.supabase = void 0;
const supabase_js_1 = require("@supabase/supabase-js");
const dotenv_1 = __importDefault(require("dotenv"));
dotenv_1.default.config();
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseKey) {
    throw new Error('Supabase URL e Key são obrigatórios. Verifique as variáveis de ambiente SUPABASE_URL e SUPABASE_ANON_KEY');
}
// Cliente Supabase
exports.supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey, {
    auth: {
        autoRefreshToken: true,
        persistSession: false, // Para backend, não precisamos persistir sessão
    },
    db: {
        schema: 'public'
    }
});
// Função para testar conexão
const testSupabaseConnection = async () => {
    try {
        const { data, error } = await exports.supabase.from('contacts').select('count').limit(1);
        if (error) {
            console.error('❌ Erro ao conectar com Supabase:', error.message);
            return false;
        }
        console.log('✅ Conexão com Supabase estabelecida com sucesso');
        return true;
    }
    catch (error) {
        console.error('❌ Erro ao testar conexão Supabase:', error);
        return false;
    }
};
exports.testSupabaseConnection = testSupabaseConnection;
exports.default = exports.supabase;
