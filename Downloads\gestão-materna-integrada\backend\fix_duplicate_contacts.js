// Script para corrigir contatos duplicados e ativar o contato principal
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variáveis SUPABASE_URL e SUPABASE_ANON_KEY são obrigatórias');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDuplicateContacts() {
  try {
    console.log('🔧 Corrigindo contatos duplicados...\n');

    // 1. Ativar o contato principal correto (Italo Cabral)
    console.log('1️⃣ Ativando contato principal...');
    const { data: mainContact, error: mainError } = await supabase
      .from('contacts')
      .update({
        is_active: true,
        baby_gender: 'male',
        last_interaction: new Date().toISOString()
      })
      .eq('id', 'b949b73d-79b2-4745-aad5-531a659e6ee8')
      .select()
      .single();

    if (mainError) {
      console.error('❌ Erro ao ativar contato principal:', mainError.message);
    } else {
      console.log('✅ Contato principal ativado:', mainContact.name);
    }

    // 2. Desativar e corrigir o contato duplicado
    console.log('\n2️⃣ Corrigindo contato duplicado...');
    const { data: duplicateContact, error: duplicateError } = await supabase
      .from('contacts')
      .update({
        name: 'Italo Cabral (DUPLICADO - DESATIVADO)',
        is_active: false,
        registration_status: 'not_interested',
        last_interaction: new Date().toISOString()
      })
      .eq('id', 'f610e418-f267-4563-a0e6-1a5c20be1f71')
      .select()
      .single();

    if (duplicateError) {
      console.error('❌ Erro ao corrigir contato duplicado:', duplicateError.message);
    } else {
      console.log('✅ Contato duplicado desativado:', duplicateContact.name);
    }

    // 3. Verificar resultado final
    console.log('\n3️⃣ Verificando resultado...');
    const { data: finalContacts, error: finalError } = await supabase
      .from('contacts')
      .select('*')
      .or('phone.eq.(84) 98850-1582,phone.eq.84988501582')
      .order('last_interaction', { ascending: false });

    if (finalError) {
      console.error('❌ Erro ao verificar resultado:', finalError.message);
    } else {
      console.log('📋 Contatos relacionados ao telefone (84) 98850-1582:');
      finalContacts.forEach((contact, index) => {
        console.log(`${index + 1}. ${contact.name}`);
        console.log(`   ID: ${contact.id}`);
        console.log(`   Telefone: ${contact.phone}`);
        console.log(`   Ativo: ${contact.is_active}`);
        console.log(`   Status: ${contact.registration_status}`);
        console.log(`   Gênero bebê: ${contact.baby_gender}\n`);
      });
    }

    // 4. Limpar outros contatos inativos antigos (opcional)
    console.log('4️⃣ Limpando contatos inativos antigos...');
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const { data: cleanedContacts, error: cleanError } = await supabase
      .from('contacts')
      .delete()
      .eq('is_active', false)
      .eq('registration_status', 'unregistered')
      .lt('last_interaction', oneWeekAgo.toISOString())
      .select();

    if (cleanError) {
      console.error('❌ Erro na limpeza:', cleanError.message);
    } else {
      console.log(`🧹 ${cleanedContacts?.length || 0} contatos antigos removidos`);
    }

  } catch (error) {
    console.error('❌ Erro geral:', error);
  }
}

// Executar script
fixDuplicateContacts()
  .then(() => {
    console.log('\n🎉 Correção de contatos concluída!');
    console.log('✅ Agora o sistema deve funcionar corretamente');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Erro fatal:', error);
    process.exit(1);
  });
