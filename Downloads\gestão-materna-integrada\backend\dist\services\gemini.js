"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.GeminiAIService = void 0;
const generative_ai_1 = require("@google/generative-ai");
// REMOVIDO: Função de upload complexa - usando método inline mais simples
// Função avançada para limpar JSON retornado pelo Gemini
function cleanGeminiJSON(text, attempt = 1) {
    try {
        console.log(`🧹 Tentativa ${attempt} - Limpando JSON do Gemini:`, text.substring(0, 100) + '...');
        let cleaned = text;
        // Estratégia 1: Remover markdown code blocks
        cleaned = cleaned.replace(/```json\s*/gi, '').replace(/```\s*/g, '');
        // Estratégia 2: Remover caracteres de controle e espaços extras
        cleaned = cleaned.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        cleaned = cleaned.trim();
        // Estratégia 3: Remover texto antes e depois do JSON
        const jsonMatch = cleaned.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            cleaned = jsonMatch[0];
        }
        // Estratégia 4: Tentar encontrar JSON em array
        if (!cleaned.startsWith('{') && cleaned.includes('[')) {
            const arrayMatch = cleaned.match(/\[[\s\S]*\]/);
            if (arrayMatch) {
                cleaned = arrayMatch[0];
            }
        }
        // Estratégia 5: Limpar caracteres problemáticos comuns
        cleaned = cleaned
            .replace(/,\s*}/g, '}') // Remove vírgulas antes de }
            .replace(/,\s*]/g, ']') // Remove vírgulas antes de ]
            .replace(/\n\s*\n/g, '\n') // Remove linhas vazias duplas
            .replace(/\t/g, ' ') // Substitui tabs por espaços
            .replace(/\r/g, ''); // Remove carriage returns
        console.log(`✅ Tentativa ${attempt} - JSON limpo:`, cleaned.substring(0, 100) + '...');
        return cleaned;
    }
    catch (error) {
        console.error(`❌ Erro na tentativa ${attempt} ao limpar JSON do Gemini:`, error);
        return text;
    }
}
// Função para tentar múltiplas estratégias de parsing
function parseJSONWithFallback(text) {
    const maxAttempts = 3;
    let lastError = null;
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
        try {
            const cleaned = cleanGeminiJSON(text, attempt);
            // Tentar parse direto
            const parsed = JSON.parse(cleaned);
            console.log(`✅ JSON parseado com sucesso na tentativa ${attempt}`);
            return parsed;
        }
        catch (error) {
            lastError = error;
            console.warn(`⚠️ Tentativa ${attempt} falhou:`, error);
            if (attempt < maxAttempts) {
                // Estratégias adicionais para tentativas subsequentes
                if (attempt === 2) {
                    // Tentar com escape de caracteres especiais
                    text = text.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
                }
                else if (attempt === 3) {
                    // Tentar extrair apenas números e strings básicas
                    const simpleMatch = text.match(/\{[^{}]*\}/);
                    if (simpleMatch) {
                        text = simpleMatch[0];
                    }
                }
            }
        }
    }
    // Se todas as tentativas falharam, logar detalhes completos
    console.error('❌ Todas as tentativas de parsing falharam');
    console.error('📄 Texto original completo:', text);
    console.error('📄 Último erro:', lastError === null || lastError === void 0 ? void 0 : lastError.message);
    throw new Error(`Falha ao fazer parse do JSON após ${maxAttempts} tentativas: ${lastError === null || lastError === void 0 ? void 0 : lastError.message}`);
}
// Função para validar e ajustar tamanho da resposta de forma inteligente
function validateResponseLength(response, minWords = 15, maxWords = 60) {
    const words = response.trim().split(/\s+/);
    const wordCount = words.length;
    console.log(`📏 Resposta da IA: ${wordCount} palavras (meta: ${minWords}-${maxWords})`);
    if (wordCount >= minWords && wordCount <= maxWords) {
        console.log('✅ Tamanho da resposta adequado');
        return response;
    }
    if (wordCount > maxWords) {
        // Truncamento inteligente: procurar por pontos de parada naturais
        const text = response.trim();
        // Tentar truncar em frases completas primeiro
        const sentences = text.split(/[.!?]+/);
        let truncated = '';
        let currentWords = 0;
        for (const sentence of sentences) {
            const sentenceWords = sentence.trim().split(/\s+/).length;
            if (currentWords + sentenceWords <= maxWords && sentence.trim()) {
                truncated += (truncated ? '. ' : '') + sentence.trim();
                currentWords += sentenceWords;
            }
            else {
                break;
            }
        }
        // Se conseguiu truncar em frases completas e tem conteúdo suficiente
        if (truncated && currentWords >= minWords) {
            console.log(`✂️ Resposta truncada inteligentemente de ${wordCount} para ${currentWords} palavras (frases completas)`);
            return truncated + (truncated.endsWith('.') || truncated.endsWith('!') || truncated.endsWith('?') ? '' : '.');
        }
        // Fallback: truncar por palavras mas tentar manter sentido
        const wordsTruncated = words.slice(0, maxWords);
        let result = wordsTruncated.join(' ');
        // Remover palavra incompleta no final se necessário
        if (result.endsWith(',') || result.endsWith(';')) {
            const lastSpaceIndex = result.lastIndexOf(' ');
            if (lastSpaceIndex > 0) {
                result = result.substring(0, lastSpaceIndex);
            }
        }
        console.log(`✂️ Resposta truncada de ${wordCount} para ${maxWords} palavras (fallback)`);
        return result + (result.endsWith('.') || result.endsWith('!') || result.endsWith('?') ? '' : '...');
    }
    if (wordCount < minWords) {
        console.log(`⚠️ Resposta muito curta (${wordCount} palavras), mantendo original`);
        return response;
    }
    return response;
}
class GeminiAIService {
    constructor() {
        this.ai = null;
        this.model = null;
        this.isInitialized = false;
        const apiKey = process.env.GEMINI_API_KEY || process.env.GOOGLE_API_KEY;
        if (!apiKey) {
            console.warn('⚠️  GEMINI_API_KEY não configurada. Serviço de IA desabilitado.');
            this.isInitialized = false;
            return;
        }
        try {
            this.ai = new generative_ai_1.GoogleGenerativeAI(apiKey);
            // ALTERADO: Usamos o modelo 1.5 Flash que suporta áudio, vídeo, etc.
            this.model = this.ai.getGenerativeModel({ model: "gemini-1.5-flash" });
            this.isInitialized = true;
            console.log('✅ Gemini AI inicializado com sucesso (modelo: gemini-1.5-flash)');
            // Iniciar limpeza automática de arquivos
            this.startAutoCleanup();
        }
        catch (error) {
            console.error('❌ Erro ao inicializar Gemini AI:', error);
            this.isInitialized = false;
        }
    }
    // NOVO: Iniciar limpeza automática
    startAutoCleanup() {
        // Executar limpeza a cada hora (3600000 ms)
        setInterval(async () => {
            await this.cleanupOldFiles();
        }, 3600000);
        // Executar primeira limpeza após 5 minutos
        setTimeout(async () => {
            await this.cleanupOldFiles();
        }, 300000);
        console.log('🧹 Limpeza automática de arquivos de áudio configurada (a cada hora)');
    }
    // ALTERADO: Método agora aceita tanto string quanto array de partes (para suporte a áudio)
    async generateContent(promptParts) {
        var _a, _b;
        if (!this.isInitialized || !this.model) {
            throw new Error('Gemini AI não está inicializado');
        }
        try {
            console.log('🤖 Enviando requisição para Gemini...');
            // Log detalhado para debug
            if (Array.isArray(promptParts)) {
                console.log(`📋 Partes da requisição: ${promptParts.length}`);
                promptParts.forEach((part, index) => {
                    if (typeof part === 'string') {
                        console.log(`   ${index + 1}. Texto: ${part.substring(0, 100)}...`);
                    }
                    else if (part.inlineData) {
                        console.log(`   ${index + 1}. Mídia: ${part.inlineData.mimeType}, ${(part.inlineData.data.length / 1024).toFixed(1)}KB`);
                    }
                });
            }
            const result = await this.model.generateContent(promptParts);
            const response = await result.response;
            const text = response.text() || '';
            console.log('✅ Resposta recebida do Gemini');
            return text;
        }
        catch (error) {
            console.error('❌ Erro detalhado do Gemini:', {
                message: error.message,
                status: error.status,
                statusText: error.statusText,
                errorDetails: error.errorDetails
            });
            // Tratamento específico para erros comuns
            if (error.status === 400) {
                if ((_a = error.message) === null || _a === void 0 ? void 0 : _a.includes('invalid argument')) {
                    throw new Error('Formato de áudio não suportado ou dados inválidos');
                }
                else if ((_b = error.message) === null || _b === void 0 ? void 0 : _b.includes('quota')) {
                    throw new Error('Cota da API Gemini excedida');
                }
            }
            throw error;
        }
    }
    async getConversationContext(_contact, _limit = 10) {
        // Contexto de conversa seria implementado com Supabase
        // Por enquanto, retornar array vazio
        console.log('📝 Contexto de conversa não implementado para Supabase');
        return [];
    }
    formatContext(messages) {
        return messages.map(msg => ({
            role: msg.fromMe ? 'assistant' : 'user',
            content: msg.content
        }));
    }
    // =========================================================
    // ATUALIZAÇÃO PRINCIPAL NESTA FUNÇÃO
    // =========================================================
    async generateResponse(contact, message, audioData) {
        try {
            const context = await this.getConversationContext(contact);
            const formattedContext = this.formatContext(context);
            const promptInstruction = `
        Você é "Rafaela", assistente virtual inspirada na Vereadora Rafaela de Parnamirim - fonoaudióloga, cristã e defensora da inclusão.

        PERSONALIDADE RAFAELA:
        🧡 Tom maternal e acolhedor como uma irmã mais velha
        ✊🏾 Lutadora incansável pelo bem-estar das gestantes
        🙏🏽 Fé cristã que traz esperança e força
        💪🏽 "Seguimos firmes juntas" - linguagem de união e força

        ESTILO DE COMUNICAÇÃO:
        - Use "minha querida", "nossa gente", "juntas somos mais fortes"
        - Emoji principal: 🧡 (quando apropriado)
        - Referências à fé: "Deus abençoa", "com fé e esperança"
        - Linguagem de ação: "seguimos firmes", "caminhando juntas"
        - Tom inclusivo e acessível

        Contexto da gestante:
        - Nome: ${contact.name}
        - Telefone: ${contact.phone}
        - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

        Histórico da conversa:
        ${formattedContext.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

        Nova mensagem da gestante: ${message}
        ${audioData ? '(IMPORTANTE: Ouça o áudio anexo e responda principalmente baseado nele. O texto é apenas contexto.)' : ''}

        DIRETRIZES IMPORTANTES:
        1. SEJA CONCISA: Máximo 50-70 palavras
        2. Tom maternal da Rafaela: "minha querida", "nossa gente"
        3. Use o nome da gestante com carinho
        4. Mencione o bebê com gênero correto se conhecido
        5. Inclua elementos de fé quando apropriado: "Deus abençoa"
        6. Linguagem de união: "seguimos firmes juntas"
        7. Sugira médico apenas se necessário
        8. Termine com força e esperança

        RESPOSTA CONCISA NO ESTILO RAFAELA (50-70 palavras):`;
            // Montamos as "partes" do prompt
            const promptParts = [promptInstruction];
            // CORREÇÃO: Converter Buffer para base64 e formatar para a API Gemini
            if (audioData && audioData.buffer.length > 0) {
                console.log(`🔊 Processando áudio para Gemini. MimeType: ${audioData.mimetype}, Tamanho: ${audioData.buffer.length} bytes.`);
                // Validar e corrigir mimeType para formatos suportados pelo Gemini
                let validMimeType = audioData.mimetype;
                // Gemini suporta: audio/wav, audio/mp3, audio/aiff, audio/aac, audio/ogg, audio/flac
                if (!validMimeType || validMimeType === 'audio/webm' || validMimeType === 'audio/opus') {
                    validMimeType = 'audio/ogg'; // Fallback para formato suportado
                    console.log(`🔄 MimeType convertido de ${audioData.mimetype} para ${validMimeType}`);
                }
                const base64Audio = audioData.buffer.toString('base64');
                // Validar tamanho do áudio (Gemini tem limite de ~20MB)
                if (audioData.buffer.length > 20 * 1024 * 1024) {
                    throw new Error('Áudio muito grande (máximo 20MB)');
                }
                const audioPart = {
                    inlineData: {
                        data: base64Audio,
                        mimeType: validMimeType,
                    },
                };
                promptParts.push(audioPart);
                console.log(`✅ Áudio preparado para Gemini: ${validMimeType}, ${(audioData.buffer.length / 1024).toFixed(1)}KB`);
            }
            console.log(`🤖 Enviando ${promptParts.length} parte(s) para o Gemini...`);
            if (audioData) {
                console.log(`   - Parte 1: Texto (prompt)`);
                console.log(`   - Parte 2: Áudio (mimeType: ${audioData.mimetype})`);
            }
            const rawResponse = await this.generateContent(promptParts);
            // Validar e ajustar tamanho da resposta (15-80 palavras para melhor qualidade)
            const response = validateResponseLength(rawResponse, 15, 80);
            // Análise de sentimento e necessidades
            const sentimentAnalysis = await this.analyzeSentiment(message, contact);
            const result = {
                response,
                sentiment: sentimentAnalysis.sentiment,
                needs: sentimentAnalysis.needs,
                suggestions: sentimentAnalysis.suggestions
            };
            // Emitir eventos para webhooks
            console.log('📊 Event middleware não implementado para Supabase');
            return result;
        }
        catch (error) {
            console.error('❌ Erro ao gerar resposta:', error);
            if (error instanceof Error && 'cause' in error) {
                console.error('Detalhes do erro da API:', error.cause);
            }
            throw error;
        }
    }
    async analyzeSentiment(message, contact) {
        const prompt = `
      Analise a seguinte mensagem de uma gestante e forneça uma análise detalhada em formato JSON:
      
      Contexto:
      - Nome: ${contact.name}
      - Estágio da gestação: Não informado
      
      Mensagem: ${message}
      
      Forneça uma análise com os seguintes campos:
      {
        "sentiment": {
          "type": "positive/negative/neutral",
          "score": 0.0 a 1.0,
          "emotions": ["lista de emoções identificadas"]
        },
        "needs": ["lista de necessidades identificadas"],
        "suggestions": ["sugestões de acompanhamento"],
        "priority": "alta/média/baixa",
        "medical_attention": true/false,
        "follow_up": ["ações de acompanhamento recomendadas"]
      }`;
        const result = await this.generateContent(prompt);
        try {
            return parseJSONWithFallback(result);
        }
        catch (parseError) {
            console.error('❌ Todas as tentativas de parse falharam:', parseError);
            console.error('📄 Resposta original do Gemini:', result);
            const manualAnalysis = this.attemptManualAnalysis(result, message);
            if (manualAnalysis) {
                console.log('🔧 Análise manual bem-sucedida');
                return manualAnalysis;
            }
            console.warn('⚠️ Usando análise padrão como último recurso');
            return {
                sentiment: {
                    type: 'neutral',
                    score: 0.5,
                    emotions: ['indefinido']
                },
                needs: ['análise automática falhou - revisar resposta da IA'],
                suggestions: ['verificar logs do sistema', 'revisar prompt da IA'],
                priority: 'média',
                medical_attention: false,
                follow_up: ['acompanhar evolução', 'revisar configuração da IA'],
                debug_info: {
                    original_response: result.substring(0, 500),
                    error: parseError.message,
                    timestamp: new Date().toISOString()
                }
            };
        }
    }
    attemptManualAnalysis(_aiResponse, originalMessage) {
        try {
            console.log('🔧 Tentando análise manual da resposta da IA');
            const message = originalMessage.toLowerCase();
            let sentimentType = 'neutral';
            let sentimentScore = 0.5;
            const emotions = [];
            const positiveWords = ['feliz', 'alegre', 'bem', 'ótimo', 'bom', 'tranquila', 'animada'];
            const negativeWords = ['triste', 'preocupada', 'dor', 'mal', 'ruim', 'medo', 'ansiedade'];
            const urgentWords = ['urgente', 'emergência', 'sangramento', 'dor forte', 'hospital'];
            if (positiveWords.some(word => message.includes(word))) {
                sentimentType = 'positive';
                sentimentScore = 0.7;
                emotions.push('alegria', 'tranquilidade');
            }
            else if (negativeWords.some(word => message.includes(word))) {
                sentimentType = 'negative';
                sentimentScore = 0.3;
                emotions.push('preocupação');
            }
            const needs = [];
            const suggestions = [];
            let priority = 'média';
            let medicalAttention = false;
            if (urgentWords.some(word => message.includes(word))) {
                needs.push('atendimento médico urgente');
                suggestions.push('entrar em contato com médico imediatamente');
                priority = 'alta';
                medicalAttention = true;
            }
            else if (message.includes('dúvida') || message.includes('pergunta')) {
                needs.push('esclarecimento');
                suggestions.push('fornecer informações educativas');
            }
            else {
                needs.push('acompanhamento de rotina');
                suggestions.push('manter contato regular');
            }
            return {
                sentiment: {
                    type: sentimentType,
                    score: sentimentScore,
                    emotions
                },
                needs,
                suggestions,
                priority,
                medical_attention: medicalAttention,
                follow_up: ['acompanhar evolução'],
                analysis_method: 'manual_fallback'
            };
        }
        catch (error) {
            console.error('❌ Erro na análise manual:', error);
            return null;
        }
    }
    async generateFollowUpMessage(contact) {
        const prompt = `
      Você é Rafaela, inspirada na Vereadora de Parnamirim. Gere uma mensagem de acompanhamento para uma gestante:

      PERSONALIDADE RAFAELA:
      🧡 Maternal, acolhedora e próxima da comunidade
      💪🏽 "Seguimos firmes juntas" - linguagem de força e união
      🙏🏽 Fé cristã que traz esperança

      Gestante:
      - Nome: ${contact.name}
      - Gênero do bebê: ${contact.babyGender === 'male' ? 'Menino' : contact.babyGender === 'female' ? 'Menina' : 'Não informado'}

      ESTILO RAFAELA (20-50 palavras):
      1. Use "minha querida", "nossa gente"
      2. Tom maternal: "como você está?"
      3. Mencione o bebê com carinho
      4. Inclua elementos de fé quando apropriado: "Deus abençoa"
      5. Linguagem de união: "seguimos juntas", "nossa família"
      6. Ofereça suporte: "estou aqui para você"
      7. Termine com força e esperança

      MENSAGEM NO ESTILO RAFAELA (20-50 palavras):`;
        const rawResult = await this.generateContent(prompt);
        const result = validateResponseLength(rawResult, 20, 50);
        return result;
    }
    async cleanupOldFiles() {
        if (!this.ai)
            return;
        try {
            console.log('🧹 Limpando arquivos antigos do Gemini...');
            // NOTA: A versão atual da biblioteca @google/generative-ai (0.1.3) não suporta
            // gerenciamento de arquivos através da instância GoogleGenerativeAI.
            // Para implementar limpeza de arquivos, seria necessário:
            // 1. Atualizar para uma versão mais recente da biblioteca
            // 2. Usar GoogleAIFileManager do pacote @google/generative-ai/server
            // 3. Ou implementar chamadas diretas para a API REST do Gemini
            console.log('⚠️ Limpeza de arquivos não implementada na versão atual da biblioteca');
            console.log('💡 Para habilitar esta funcionalidade, atualize @google/generative-ai para versão >= 0.12.0');
            // Implementação alternativa usando fetch direto para a API REST seria:
            // const response = await fetch('https://generativelanguage.googleapis.com/v1beta/files', {
            //   headers: { 'Authorization': `Bearer ${apiKey}` }
            // });
            // const files = await response.json();
            // ... lógica de limpeza ...
        }
        catch (error) {
            console.error('❌ Erro na limpeza de arquivos:', error);
        }
    }
}
exports.GeminiAIService = GeminiAIService;
