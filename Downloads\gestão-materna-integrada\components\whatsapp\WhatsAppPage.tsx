
import React, { useState, useEffect, useCallback } from 'react';
import PageTitle from '../shared/PageTitle';
import WhatsAppConnectCard from './WhatsAppConnectCard';
import ConversationView from './ConversationView';
import { PregnantWoman, Message, WhatsAppConnectionStatus } from '../../types';
import { Contact } from '../../src/types';
import { apiService } from '../../src/services/apiService';
import Select from './Select'; // A new Select component
import Spinner from '../shared/Spinner';
import { io, Socket } from 'socket.io-client';

// Função para mapear Contact para PregnantWoman
const mapContactToPregnantWoman = (contact: Contact): PregnantWoman => {
  return {
    id: contact._id,
    name: contact.name,
    phone: contact.phone,
    email: contact.email || '',
    dueDate: contact.dueDate ? new Date(contact.dueDate).toISOString() : '',
    observations: contact.observations || contact.notes || '',
    createdAt: new Date(contact.createdAt).toISOString(),
    age: undefined // Pode ser calculado se necessário
  };
};

// Service for fetching real pregnant women from API
const fetchPregnantWomenNames = async (): Promise<{ id: string, name: string }[]> => {
  try {
    const pregnantWomen = await apiService.getPregnantWomen();
    return pregnantWomen.map(p => ({
      id: p._id || p.id,
      name: p.name
    }));
  } catch (error) {
    console.error('Erro ao buscar gestantes:', error);
    return [];
  }
};

const WhatsAppPage: React.FC = () => {
  const [connectionStatus, setConnectionStatus] = useState<WhatsAppConnectionStatus>(WhatsAppConnectionStatus.DISCONNECTED);
  const [qrCodeUrl, setQrCodeUrl] = useState<string | null>(null);
  const [pregnantWomenOptions, setPregnantWomenOptions] = useState<{ value: string; label: string }[]>([]);
  const [allPregnantWomen, setAllPregnantWomen] = useState<PregnantWoman[]>([]);
  const [selectedPregnantWoman, setSelectedPregnantWoman] = useState<PregnantWoman | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loadingContacts, setLoadingContacts] = useState(true);
  const [socket, setSocket] = useState<Socket | null>(null);
  const [realTimeMessages, setRealTimeMessages] = useState<any[]>([]);

  // Verificar status do WhatsApp (sem Socket.IO por enquanto)
  useEffect(() => {
    console.log('🔌 Verificando status do WhatsApp...');

    // Verificar status inicial do WhatsApp
    const checkWhatsAppStatus = async () => {
      try {
        const response = await fetch('http://localhost:3334/api/whatsapp-auto/status');
        const data = await response.json();
        console.log('📱 Status WhatsApp:', data);

        if (data.success && data.data.connection.isConnected) {
          setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
        } else {
          setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
        }
      } catch (err) {
        console.error('❌ Erro ao verificar status WhatsApp:', err);
        setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
      }
    };

    checkWhatsAppStatus();

    // Escutar eventos do WhatsApp
    newSocket.on('status', (status: string) => {
      console.log('📱 Status WhatsApp atualizado:', status);
      if (status === 'connected') {
        setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
        setQrCodeUrl(null);
      } else if (status === 'disconnected') {
        setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
        setQrCodeUrl(null);
      }
    });

    // Escutar mensagens em tempo real
    newSocket.on('message', (messageData: any) => {
      console.log('📱 Nova mensagem recebida:', messageData);
      setRealTimeMessages(prev => [...prev, messageData]);
    });

    newSocket.on('newMessage', (messageData: any) => {
      console.log('📱 Nova mensagem (newMessage):', messageData);
      setRealTimeMessages(prev => [...prev, messageData]);
    });

    return () => {
      console.log('🔌 Desconectando Socket.IO...');
      newSocket.disconnect();
    };
  }, []);

  // Carregar gestantes
  useEffect(() => {
    const loadContacts = async () => {
      setLoadingContacts(true);
      try {
        // Carregar gestantes reais da API
        const contacts = await apiService.getContacts();
        const pregnantWomen = contacts.map(mapContactToPregnantWoman);
        setAllPregnantWomen(pregnantWomen);

        // Criar opções para o dropdown
        const options = pregnantWomen.map(p => ({
          value: p.id,
          label: p.name
        }));
        setPregnantWomenOptions(options);

        console.log('✅ Gestantes carregadas para WhatsApp:', pregnantWomen.length);
      } catch (error) {
        console.error('❌ Erro ao carregar gestantes:', error);
      } finally {
        setLoadingContacts(false);
      }
    };
    loadContacts();
  }, []);

  // Conectar ao WhatsApp real
  const handleConnect = useCallback(() => {
    console.log('🔌 Tentando conectar ao WhatsApp...');
    setConnectionStatus(WhatsAppConnectionStatus.CONNECTING);

    // O WhatsApp já está conectado automaticamente no backend
    // Apenas verificar o status
    fetch('http://localhost:3000/api/whatsapp/status')
      .then(res => res.json())
      .then(data => {
        console.log('📱 Status WhatsApp após conectar:', data);
        if (data.isConnected) {
          setConnectionStatus(WhatsAppConnectionStatus.CONNECTED);
          setQrCodeUrl(null);
        } else {
          setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
        }
      })
      .catch(err => {
        console.error('❌ Erro ao conectar WhatsApp:', err);
        setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
      });
  }, []);

  const handleDisconnect = () => {
    console.log('🔌 Desconectando WhatsApp...');
    setConnectionStatus(WhatsAppConnectionStatus.DISCONNECTED);
    setQrCodeUrl(null);
  };
  
  const handleSelectPregnantWoman = async (id: string) => {
    const woman = allPregnantWomen.find(p => p.id === id);
    if (woman) {
      setSelectedPregnantWoman(woman);
      console.log('✅ Gestante selecionada:', woman.name);

      // Carregar mensagens reais desta gestante
      try {
        const response = await fetch(`http://localhost:3000/api/contacts/${id}/messages`);
        if (response.ok) {
          const realMessages = await response.json();
          console.log('📱 Mensagens carregadas:', realMessages.length);

          // Converter mensagens do backend para o formato do frontend
          const formattedMessages: Message[] = realMessages.map((msg: any) => ({
            id: msg._id,
            sender: msg.fromMe ? 'user' : 'gestante',
            text: msg.content,
            timestamp: msg.timestamp,
            status: 'delivered'
          }));

          setMessages(formattedMessages);
        } else {
          console.log('📱 Nenhuma mensagem encontrada para esta gestante');
          setMessages([]);
        }
      } catch (error) {
        console.error('❌ Erro ao carregar mensagens:', error);
        setMessages([]);
      }
    } else {
      setSelectedPregnantWoman(null);
      setMessages([]);
      console.log('❌ Gestante não encontrada:', id);
    }
  };

  const handleSendMessage = async (text: string): Promise<Message> => {
    if (!selectedPregnantWoman) {
      throw new Error('Nenhuma gestante selecionada');
    }

    const newMessage: Message = {
      id: `msg_${Date.now()}`,
      sender: 'user',
      text,
      timestamp: new Date().toISOString(),
      status: 'sending',
    };

    // Adicionar mensagem localmente primeiro
    setMessages(prev => [...prev, newMessage]);

    try {
      // Enviar mensagem via API
      const response = await fetch('http://localhost:3000/api/whatsapp/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: selectedPregnantWoman.phone,
          message: text
        })
      });

      if (response.ok) {
        console.log('✅ Mensagem enviada com sucesso');
        // Atualizar status da mensagem
        setMessages(prev => prev.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'sent' } : msg
        ));
      } else {
        console.error('❌ Erro ao enviar mensagem');
        setMessages(prev => prev.map(msg =>
          msg.id === newMessage.id ? { ...msg, status: 'failed' } : msg
        ));
      }
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === newMessage.id ? { ...msg, status: 'failed' } : msg
      ));
    }

    return newMessage;
  };

  const handleSendAiMessage = async (text: string): Promise<Message> => {
    if (!selectedPregnantWoman) {
      throw new Error('Nenhuma gestante selecionada');
    }

    const aiMessage: Message = {
      id: `msg_ai_${Date.now()}`,
      sender: 'ai',
      text,
      timestamp: new Date().toISOString(),
      status: 'sending',
    };

    // Adicionar mensagem localmente primeiro
    setMessages(prev => [...prev, aiMessage]);

    try {
      // Enviar mensagem via API
      const response = await fetch('http://localhost:3000/api/whatsapp/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          to: selectedPregnantWoman.phone,
          message: text
        })
      });

      if (response.ok) {
        console.log('✅ Mensagem da IA enviada com sucesso');
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessage.id ? { ...msg, status: 'sent' } : msg
        ));
      } else {
        console.error('❌ Erro ao enviar mensagem da IA');
        setMessages(prev => prev.map(msg =>
          msg.id === aiMessage.id ? { ...msg, status: 'failed' } : msg
        ));
      }
    } catch (error) {
      console.error('❌ Erro ao enviar mensagem da IA:', error);
      setMessages(prev => prev.map(msg =>
        msg.id === aiMessage.id ? { ...msg, status: 'failed' } : msg
      ));
    }

    return aiMessage;
  };


  return (
    <div>
      <PageTitle title="WhatsApp & Inteligência Artificial" subtitle="Conecte-se ao WhatsApp e utilize a IA para interações." />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <WhatsAppConnectCard 
            status={connectionStatus}
            qrCodeUrl={qrCodeUrl}
            onConnect={handleConnect}
            onDisconnect={handleDisconnect}
          />
          <div className="mt-6 bg-white p-4 rounded-lg shadow">
            <h3 className="text-lg font-semibold text-neutral-dark mb-3">Selecionar Gestante</h3>
            {loadingContacts ? <Spinner /> : (
              <Select
                options={pregnantWomenOptions}
                value={selectedPregnantWoman?.id || ''}
                onChange={(e) => handleSelectPregnantWoman(e.target.value)}
                placeholder="Escolha uma gestante..."
              />
            )}
          </div>
        </div>

        <div className="lg:col-span-2">
          {selectedPregnantWoman ? (
            <ConversationView 
              contactName={selectedPregnantWoman.name}
              messages={messages}
              onSendMessage={handleSendMessage}
              onSendAiMessage={handleSendAiMessage}
              currentPregnantWoman={selectedPregnantWoman}
            />
          ) : (
            <div className="bg-white p-6 rounded-lg shadow h-full flex items-center justify-center">
              <p className="text-gray-500">Selecione uma gestante para visualizar ou iniciar uma conversa.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WhatsAppPage;
    