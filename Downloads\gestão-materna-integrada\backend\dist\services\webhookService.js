"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WhatsAppWebhookEvents = exports.SystemWebhookEvents = exports.webhookService = void 0;
const axios_1 = __importDefault(require("axios"));
const events_1 = require("events");
class WebhookService extends events_1.EventEmitter {
    constructor() {
        super();
        this.endpoints = new Map();
        this.eventQueue = [];
        this.processing = false;
        this.startEventProcessor();
    }
    // Registrar um novo webhook endpoint
    registerEndpoint(endpoint) {
        const id = `webhook_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const webhookEndpoint = {
            id,
            ...endpoint,
            retryAttempts: endpoint.retryAttempts || 3,
            active: endpoint.active !== false
        };
        this.endpoints.set(id, webhookEndpoint);
        console.log(`📡 Webhook endpoint registrado: ${id} -> ${endpoint.url}`);
        return id;
    }
    // Remover webhook endpoint
    unregisterEndpoint(id) {
        const removed = this.endpoints.delete(id);
        if (removed) {
            console.log(`📡 Webhook endpoint removido: ${id}`);
        }
        return removed;
    }
    // Listar endpoints ativos
    getEndpoints() {
        return Array.from(this.endpoints.values());
    }
    // Atualizar endpoint
    updateEndpoint(id, updates) {
        const endpoint = this.endpoints.get(id);
        if (!endpoint)
            return false;
        const updated = { ...endpoint, ...updates };
        this.endpoints.set(id, updated);
        console.log(`📡 Webhook endpoint atualizado: ${id}`);
        return true;
    }
    // Emitir evento para webhooks
    async emitWebhookEvent(event) {
        const webhookEvent = {
            id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            timestamp: new Date(),
            ...event
        };
        this.eventQueue.push(webhookEvent);
        this.emit('event', webhookEvent);
        console.log(`📡 Evento webhook enfileirado: ${webhookEvent.type} (${webhookEvent.id})`);
    }
    // Processar fila de eventos
    async startEventProcessor() {
        setInterval(async () => {
            if (this.processing || this.eventQueue.length === 0)
                return;
            this.processing = true;
            try {
                const event = this.eventQueue.shift();
                if (event) {
                    await this.processEvent(event);
                }
            }
            catch (error) {
                console.error('❌ Erro ao processar evento webhook:', error);
            }
            finally {
                this.processing = false;
            }
        }, 1000); // Processar a cada 1 segundo
    }
    // Processar um evento específico
    async processEvent(event) {
        const relevantEndpoints = Array.from(this.endpoints.values())
            .filter(endpoint => endpoint.active &&
            (endpoint.events.includes(event.type) || endpoint.events.includes('*')));
        if (relevantEndpoints.length === 0) {
            console.log(`📡 Nenhum endpoint configurado para evento: ${event.type}`);
            return;
        }
        const promises = relevantEndpoints.map(endpoint => this.sendToEndpoint(endpoint, event));
        await Promise.allSettled(promises);
    }
    // Enviar evento para um endpoint específico
    async sendToEndpoint(endpoint, event) {
        var _a, _b;
        let attempts = 0;
        const maxAttempts = endpoint.retryAttempts;
        while (attempts < maxAttempts) {
            try {
                const payload = {
                    event: {
                        id: event.id,
                        type: event.type,
                        timestamp: event.timestamp.toISOString(),
                        data: event.data,
                        source: event.source
                    },
                    webhook: {
                        id: endpoint.id,
                        attempt: attempts + 1,
                        maxAttempts
                    }
                };
                const headers = {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Rafaela-Webhook/1.0'
                };
                // Adicionar assinatura se secret estiver configurado
                if (endpoint.secret) {
                    const crypto = require('crypto');
                    const signature = crypto
                        .createHmac('sha256', endpoint.secret)
                        .update(JSON.stringify(payload))
                        .digest('hex');
                    headers['X-Webhook-Signature'] = `sha256=${signature}`;
                }
                const response = await axios_1.default.post(endpoint.url, payload, {
                    headers,
                    timeout: 10000, // 10 segundos
                    validateStatus: (status) => status >= 200 && status < 300
                });
                // Sucesso
                this.endpoints.set(endpoint.id, {
                    ...endpoint,
                    lastSuccess: new Date(),
                    lastError: undefined
                });
                console.log(`📡 ✅ Webhook enviado com sucesso: ${endpoint.id} -> ${event.type} (${response.status})`);
                return;
            }
            catch (error) {
                attempts++;
                const errorMessage = ((_b = (_a = error.response) === null || _a === void 0 ? void 0 : _a.data) === null || _b === void 0 ? void 0 : _b.message) || error.message || 'Erro desconhecido';
                console.log(`📡 ❌ Tentativa ${attempts}/${maxAttempts} falhou para ${endpoint.id}: ${errorMessage}`);
                if (attempts < maxAttempts) {
                    // Aguardar antes de tentar novamente (backoff exponencial)
                    const delay = Math.min(1000 * Math.pow(2, attempts - 1), 30000);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
                else {
                    // Todas as tentativas falharam
                    this.endpoints.set(endpoint.id, {
                        ...endpoint,
                        lastError: errorMessage
                    });
                    console.error(`📡 ❌ Webhook falhou após ${maxAttempts} tentativas: ${endpoint.id} -> ${event.type}`);
                }
            }
        }
    }
    // Testar um endpoint
    async testEndpoint(id) {
        const endpoint = this.endpoints.get(id);
        if (!endpoint) {
            return { success: false, message: 'Endpoint não encontrado' };
        }
        const startTime = Date.now();
        try {
            const testEvent = {
                id: `test_${Date.now()}`,
                type: 'connection_status',
                timestamp: new Date(),
                data: { status: 'test', message: 'Este é um evento de teste' },
                source: 'system'
            };
            await this.sendToEndpoint(endpoint, testEvent);
            const responseTime = Date.now() - startTime;
            return {
                success: true,
                message: 'Teste realizado com sucesso',
                responseTime
            };
        }
        catch (error) {
            const responseTime = Date.now() - startTime;
            return {
                success: false,
                message: error.message || 'Erro no teste',
                responseTime
            };
        }
    }
    // Obter estatísticas dos webhooks
    getStats() {
        const endpoints = Array.from(this.endpoints.values());
        return {
            totalEndpoints: endpoints.length,
            activeEndpoints: endpoints.filter(e => e.active).length,
            queueSize: this.eventQueue.length,
            processing: this.processing
        };
    }
    // Limpar fila de eventos
    clearQueue() {
        const size = this.eventQueue.length;
        this.eventQueue = [];
        console.log(`📡 Fila de eventos limpa: ${size} eventos removidos`);
        return size;
    }
    // Pausar/retomar processamento
    pauseProcessing() {
        this.processing = true;
        console.log('📡 Processamento de webhooks pausado');
    }
    resumeProcessing() {
        this.processing = false;
        console.log('📡 Processamento de webhooks retomado');
    }
}
// Instância singleton
exports.webhookService = new WebhookService();
// Eventos específicos do sistema
exports.SystemWebhookEvents = {
    // WhatsApp Events
    MESSAGE_SENT: 'message_sent',
    MESSAGE_DELIVERED: 'message_delivered',
    MESSAGE_READ: 'message_read',
    CONNECTION_STATUS: 'connection_status',
    QR_UPDATED: 'qr_updated',
    ERROR: 'error',
    // Contact Events
    CONTACT_CREATED: 'contact_created',
    CONTACT_UPDATED: 'contact_updated',
    CONTACT_DELETED: 'contact_deleted',
    // Schedule Events
    SCHEDULE_CREATED: 'schedule_created',
    SCHEDULE_EXECUTED: 'schedule_executed',
    // AI Events
    AI_RESPONSE_GENERATED: 'ai_response_generated',
    SENTIMENT_ANALYZED: 'sentiment_analyzed',
    // Health Events
    HEALTH_CHECK_FAILED: 'health_check_failed',
    RESOURCE_WARNING: 'resource_warning',
    // System Events
    SYSTEM_STARTUP: 'system_startup',
    SYSTEM_SHUTDOWN: 'system_shutdown',
    BULK_MESSAGE_COMPLETED: 'bulk_message_completed',
    TEMPLATE_USED: 'template_used',
    // Auth Events
    USER_LOGIN: 'user_login',
    USER_LOGOUT: 'user_logout',
    // Data Events
    DATA_EXPORT: 'data_export',
    DATA_IMPORT: 'data_import'
};
// Manter compatibilidade com código existente
exports.WhatsAppWebhookEvents = {
    MESSAGE_SENT: exports.SystemWebhookEvents.MESSAGE_SENT,
    MESSAGE_DELIVERED: exports.SystemWebhookEvents.MESSAGE_DELIVERED,
    MESSAGE_READ: exports.SystemWebhookEvents.MESSAGE_READ,
    CONNECTION_STATUS: exports.SystemWebhookEvents.CONNECTION_STATUS,
    QR_UPDATED: exports.SystemWebhookEvents.QR_UPDATED,
    ERROR: exports.SystemWebhookEvents.ERROR
};
exports.default = exports.webhookService;
