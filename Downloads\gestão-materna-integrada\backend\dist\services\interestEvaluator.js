"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InterestEvaluatorService = void 0;
/**
 * Serviço para avaliar se pessoa não cadastrada está falando sobre o projeto gestantes
 */
class InterestEvaluatorService {
    constructor(geminiService) {
        this.geminiService = geminiService;
    }
    /**
     * Analisar se a mensagem está relacionada ao projeto gestantes
     */
    async analyzeIfRelatedToProject(message) {
        try {
            const prompt = `
        Analise se esta mensagem está relacionada ao projeto de acompanhamento de gestantes.

        Mensagem: "${message}"

        CRITÉRIOS PARA SER RELACIONADO:
        - Menciona gravidez, gestação, bebê, pré-natal
        - Fala sobre saúde maternal
        - Pede ajuda com questões de gravidez
        - Demonstra interesse em acompanhamento
        - Pergunta sobre serviços para gestantes

        CRITÉRIOS PARA NÃO SER RELACIONADO:
        - Mensagens genéricas (oi, olá, tudo bem)
        - Assuntos não relacionados à gravidez
        - Perguntas sobre outros temas
        - Mensagens de erro/engano

        Responda APENAS com JSON válido:
        {
          "isRelated": true/false,
          "shouldOfferRegistration": true/false,
          "confidence": número_entre_0_e_100,
          "reasoning": "explicação_breve"
        }`;
            const response = await this.geminiService.generateContent(prompt);
            const analysis = JSON.parse(this.cleanJSON(response));
            // Validar resposta
            analysis.confidence = Math.max(0, Math.min(100, analysis.confidence || 0));
            return {
                isRelated: analysis.isRelated || false,
                shouldOfferRegistration: analysis.shouldOfferRegistration || false,
                confidence: analysis.confidence
            };
        }
        catch (error) {
            console.error('❌ Erro ao analisar mensagem:', error);
            // Fallback: análise manual básica
            return this.basicProjectAnalysis(message);
        }
    }
    /**
     * Gerar resposta oferecendo cadastro
     */
    async generateRegistrationOffer(message) {
        try {
            const prompt = `
        Uma pessoa não cadastrada enviou uma mensagem relacionada ao projeto de gestantes.
        Ofereça o cadastro de forma acolhedora.

        Mensagem recebida: "${message}"

        DIRETRIZES:
        1. Seja acolhedora e maternal (estilo Rafaela)
        2. Explique brevemente que é um serviço de acompanhamento
        3. Solicite os dados: Nome completo, telefone, gênero do bebê (opcional)
        4. Use tom caloroso mas profissional
        5. Use 40-80 palavras

        RESPOSTA OFERECENDO CADASTRO (40-80 palavras):`;
            const response = await this.geminiService.generateContent(prompt);
            return this.validateResponseLength(response, 40, 80);
        }
        catch (error) {
            console.error('❌ Erro ao gerar oferta de cadastro:', error);
            return 'Olá! Que bom saber que você está grávida! Sou Rafaela e posso acompanhá-la durante sua gestação. Para começar, preciso de seu nome completo, telefone e, se quiser, o gênero do bebê. Como posso chamá-la?';
        }
    }
    /**
     * Análise manual básica (fallback)
     */
    basicProjectAnalysis(message) {
        const lowerMessage = message.toLowerCase();
        // Palavras que indicam relação com projeto gestantes
        const pregnancyWords = [
            'grávida', 'gestante', 'gravidez', 'gestação', 'bebê', 'bebe', 'criança',
            'pré-natal', 'prenatal', 'consulta', 'médico', 'obstetra', 'ginecologista',
            'ultrassom', 'exame', 'trimestre', 'semanas', 'meses', 'parto', 'nascimento',
            'maternidade', 'maternal', 'mãe', 'mamãe', 'filho', 'filha', 'menino', 'menina'
        ];
        // Palavras que indicam interesse em acompanhamento
        const interestWords = [
            'ajuda', 'acompanhamento', 'orientação', 'dúvida', 'pergunta', 'informação',
            'cuidado', 'saúde', 'bem-estar', 'apoio', 'suporte'
        ];
        // Verificar se contém palavras relacionadas
        const hasPregnancyWords = pregnancyWords.some(word => lowerMessage.includes(word));
        const hasInterestWords = interestWords.some(word => lowerMessage.includes(word));
        // Mensagens muito genéricas
        const genericMessages = ['oi', 'olá', 'ola', 'tudo bem', 'como vai', 'hey', 'e ai'];
        const isGeneric = genericMessages.some(word => lowerMessage.trim() === word);
        let isRelated = false;
        let shouldOfferRegistration = false;
        let confidence = 0;
        if (hasPregnancyWords) {
            isRelated = true;
            shouldOfferRegistration = true;
            confidence = 85;
        }
        else if (hasInterestWords && !isGeneric) {
            isRelated = true;
            shouldOfferRegistration = false; // Precisa de mais contexto
            confidence = 60;
        }
        else if (isGeneric) {
            isRelated = false;
            shouldOfferRegistration = false;
            confidence = 90;
        }
        else {
            isRelated = false;
            shouldOfferRegistration = false;
            confidence = 70;
        }
        return {
            isRelated,
            shouldOfferRegistration,
            confidence
        };
    }
    /**
     * Processar dados de cadastro fornecidos
     */
    async processRegistrationData(contact, message) {
        try {
            // Usar IA para extrair dados da mensagem
            const extractedData = await this.extractRegistrationData(message);
            if (extractedData.name && extractedData.phone) {
                // Dados completos - finalizar cadastro
                // Nota: Atualização seria feita via contactService do Supabase
                console.log('✅ Cadastro finalizado:', {
                    name: extractedData.name,
                    babyGender: extractedData.babyGender || 'unknown',
                    registrationStatus: 'registered'
                });
                return {
                    success: true,
                    response: `Perfeito, ${extractedData.name}! Seu cadastro foi realizado com sucesso. Agora posso acompanhá-la durante sua jornada maternal. Como está se sentindo hoje?`,
                    needsMoreData: false,
                    extractedData
                };
            }
            else {
                // Dados incompletos - solicitar mais informações
                const missingFields = [];
                if (!extractedData.name)
                    missingFields.push('nome completo');
                if (!extractedData.phone)
                    missingFields.push('telefone');
                return {
                    success: false,
                    response: `Preciso de mais algumas informações. Por favor, me informe: ${missingFields.join(' e ')}.`,
                    needsMoreData: true,
                    extractedData
                };
            }
        }
        catch (error) {
            console.error('❌ Erro ao processar dados de cadastro:', error);
            return {
                success: false,
                response: 'Desculpe, tive dificuldade para processar suas informações. Pode repetir seu nome completo e telefone?',
                needsMoreData: true
            };
        }
    }
    /**
     * Extrair dados de cadastro da mensagem usando IA
     */
    async extractRegistrationData(message) {
        const prompt = `
      Extraia dados de cadastro desta mensagem:
      
      "${message}"
      
      Responda APENAS com JSON válido:
      {
        "name": "nome_completo_ou_null",
        "phone": "telefone_ou_null",
        "babyGender": "male/female/unknown"
      }
      
      REGRAS:
      - name: apenas se tiver nome E sobrenome
      - phone: apenas números de telefone válidos
      - babyGender: "male" para menino, "female" para menina, "unknown" se não mencionado`;
        try {
            const response = await this.geminiService.generateContent(prompt);
            return JSON.parse(this.cleanJSON(response));
        }
        catch (error) {
            console.error('❌ Erro ao extrair dados:', error);
            return {};
        }
    }
    /**
     * Limpar JSON da resposta da IA
     */
    cleanJSON(text) {
        return text.replace(/```json\s*/g, '').replace(/```\s*/g, '').trim();
    }
    /**
     * Validar tamanho da resposta
     */
    validateResponseLength(response, minWords, maxWords) {
        const words = response.trim().split(/\s+/);
        if (words.length > maxWords) {
            return words.slice(0, maxWords).join(' ') + '.';
        }
        return response;
    }
}
exports.InterestEvaluatorService = InterestEvaluatorService;
exports.default = InterestEvaluatorService;
