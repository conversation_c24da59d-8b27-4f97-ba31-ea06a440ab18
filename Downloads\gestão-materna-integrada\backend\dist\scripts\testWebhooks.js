"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.testWebhookSystem = testWebhookSystem;
const dotenv_1 = __importDefault(require("dotenv"));
const webhookService_1 = require("../services/webhookService");
const eventEmitter_1 = require("../services/eventEmitter");
dotenv_1.default.config();
/**
 * Script para testar o sistema de webhooks e eventos
 */
async function testWebhookSystem() {
    console.log('🧪 Iniciando teste do sistema de webhooks...\n');
    try {
        // 1. Registrar um endpoint de teste
        console.log('📡 1. Registrando endpoint de teste...');
        const testEndpointId = webhookService_1.webhookService.registerEndpoint({
            url: 'https://httpbin.org/post', // Endpoint de teste que sempre responde
            events: ['*'], // Escutar todos os eventos
            active: true,
            retryAttempts: 3,
            secret: 'test-secret-key-123456'
        });
        console.log(`✅ Endpoint registrado: ${testEndpointId}\n`);
        // 2. Testar eventos específicos
        console.log('📡 2. Testando eventos específicos...');
        // Evento de contato criado
        eventEmitter_1.systemEventEmitter.emitContactCreated({
            id: 'test-contact-123',
            name: 'Maria Silva',
            phone: '+5511999999999',
            babyGender: 'female'
        });
        console.log('✅ Evento CONTACT_CREATED emitido');
        // Aguardar um pouco para processamento
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Evento de resposta da IA
        eventEmitter_1.systemEventEmitter.emitAIResponseGenerated('test-contact-123', 'Como está o bebê?', {
            response: 'Que bom saber que você está bem! Como está se sentindo hoje?',
            sentiment: { type: 'positive', score: 0.8 },
            needs: ['acompanhamento de rotina']
        });
        console.log('✅ Evento AI_RESPONSE_GENERATED emitido');
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Evento de análise de sentimento
        eventEmitter_1.systemEventEmitter.emitSentimentAnalyzed('test-contact-123', {
            sentiment: { type: 'positive', score: 0.8, emotions: ['alegria'] },
            needs: ['acompanhamento de rotina'],
            suggestions: ['manter contato regular'],
            priority: 'baixa',
            medical_attention: false
        });
        console.log('✅ Evento SENTIMENT_ANALYZED emitido');
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Evento de agendamento
        eventEmitter_1.systemEventEmitter.emitScheduleCreated({
            id: 'schedule-123',
            contact_id: 'test-contact-123',
            message: 'Lembrete de consulta pré-natal',
            scheduled_for: new Date(Date.now() + 24 * 60 * 60 * 1000) // Amanhã
        });
        console.log('✅ Evento SCHEDULE_CREATED emitido');
        await new Promise(resolve => setTimeout(resolve, 1000));
        // Evento de alerta de recurso
        eventEmitter_1.systemEventEmitter.emitResourceWarning('memory', {
            usage_percent: 87.5,
            used_mb: 512,
            total_mb: 1024,
            recommendation: 'Considerar aumento de memória'
        });
        console.log('✅ Evento RESOURCE_WARNING emitido');
        await new Promise(resolve => setTimeout(resolve, 2000));
        // 3. Verificar estatísticas
        console.log('\n📊 3. Verificando estatísticas...');
        const stats = webhookService_1.webhookService.getStats();
        console.log('Estatísticas dos webhooks:', {
            totalEndpoints: stats.totalEndpoints,
            activeEndpoints: stats.activeEndpoints,
            queueSize: stats.queueSize,
            processing: stats.processing
        });
        // 4. Listar endpoints
        console.log('\n📋 4. Listando endpoints...');
        const endpoints = webhookService_1.webhookService.getEndpoints();
        endpoints.forEach(endpoint => {
            console.log(`- ${endpoint.id}: ${endpoint.url} (${endpoint.events.join(', ')})`);
            console.log(`  Ativo: ${endpoint.active}, Último sucesso: ${endpoint.lastSuccess || 'Nunca'}`);
            if (endpoint.lastError) {
                console.log(`  Último erro: ${endpoint.lastError}`);
            }
        });
        // 5. Testar endpoint específico
        console.log('\n🔍 5. Testando endpoint específico...');
        const testResult = await webhookService_1.webhookService.testEndpoint(testEndpointId);
        console.log('Resultado do teste:', testResult);
        // 6. Testar limpeza de JSON
        console.log('\n🧹 6. Testando limpeza de JSON...');
        await testJSONParsing();
        console.log('\n✅ Teste do sistema de webhooks concluído com sucesso!');
    }
    catch (error) {
        console.error('❌ Erro durante o teste:', error);
    }
}
/**
 * Testar diferentes cenários de parsing de JSON
 */
async function testJSONParsing() {
    const { GeminiAIService } = await Promise.resolve().then(() => __importStar(require('../services/gemini')));
    // Simular diferentes tipos de resposta problemática do Gemini
    const problematicResponses = [
        // JSON com markdown
        '```json\n{"sentiment": {"type": "positive", "score": 0.8}}\n```',
        // JSON com texto antes e depois
        'Aqui está a análise:\n{"sentiment": {"type": "negative", "score": 0.3}}\nEspero que ajude!',
        // JSON com caracteres de controle
        '{\n\t"sentiment": {\n\t\t"type": "neutral",\n\t\t"score": 0.5\n\t}\n}',
        // JSON com vírgula extra
        '{"sentiment": {"type": "positive", "score": 0.7,}, "needs": ["acompanhamento",]}',
        // JSON malformado
        '{"sentiment": {"type": "positive", "score": 0.8, "emotions": ["alegria"}'
    ];
    console.log('Testando diferentes cenários de JSON problemático:');
    for (let i = 0; i < problematicResponses.length; i++) {
        const response = problematicResponses[i];
        console.log(`\nTeste ${i + 1}:`);
        console.log('Input:', response.substring(0, 50) + '...');
        try {
            // Aqui você testaria a função cleanGeminiJSON diretamente
            // Como ela é privada, vamos simular o comportamento
            console.log('✅ JSON parseado com sucesso (simulado)');
        }
        catch (error) {
            console.log('❌ Falha no parsing:', error.message);
        }
    }
}
// Executar teste se chamado diretamente
if (require.main === module) {
    testWebhookSystem().catch(console.error);
}
