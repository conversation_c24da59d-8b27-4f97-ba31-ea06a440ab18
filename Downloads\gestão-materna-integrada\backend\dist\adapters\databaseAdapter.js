"use strict";
/**
 * Adaptador para migração gradual do MongoDB para Supabase
 * Permite usar ambos os bancos durante a transição
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.dbAdapter = exports.DatabaseAdapter = void 0;
const contactService_1 = require("../services/contactService");
const messageService_1 = require("../services/messageService");
// Importações do MongoDB (existentes)
const Contact_1 = require("../models/Contact");
const Message_1 = require("../models/Message");
class DatabaseAdapter {
    constructor() {
        // Determinar qual banco usar baseado na variável de ambiente
        this.useSupabase = process.env.USE_SUPABASE === 'true';
        console.log(`🔄 DatabaseAdapter inicializado - Usando: ${this.useSupabase ? 'Supabase' : 'MongoDB'}`);
    }
    // =====================================================
    // MÉTODOS PARA CONTACTS
    // =====================================================
    async findAllContacts(filters = {}) {
        if (this.useSupabase) {
            return await contactService_1.contactService.findAll({
                isActive: filters.isActive,
                limit: filters.limit,
                offset: filters.skip,
                search: filters.search
            });
        }
        else {
            // Usar MongoDB
            let query = Contact_1.Contact.find();
            if (filters.isActive !== undefined) {
                query = query.where('isActive', filters.isActive);
            }
            if (filters.limit) {
                query = query.limit(filters.limit);
            }
            if (filters.skip) {
                query = query.skip(filters.skip);
            }
            return await query.sort({ lastInteraction: -1 }).lean();
        }
    }
    async findContactById(id) {
        if (this.useSupabase) {
            return await contactService_1.contactService.findById(id);
        }
        else {
            return await Contact_1.Contact.findById(id).lean();
        }
    }
    async findContactByPhone(phone) {
        if (this.useSupabase) {
            return await contactService_1.contactService.findByPhone(phone);
        }
        else {
            return await Contact_1.Contact.findOne({ phone }).lean();
        }
    }
    async createContact(contactData) {
        if (this.useSupabase) {
            // Mapear campos do MongoDB para Supabase
            const supaData = {
                name: contactData.name,
                phone: contactData.phone,
                baby_gender: contactData.babyGender || 'unknown',
                is_active: contactData.isActive !== undefined ? contactData.isActive : true,
                last_interaction: contactData.lastInteraction || new Date().toISOString(),
                created_by: contactData.createdBy,
                registration_status: contactData.registrationStatus || 'unregistered',
                evaluation_start_date: contactData.evaluationStartDate,
                evaluation_messages: contactData.evaluationMessages || 0,
                interest_score: contactData.interestScore || 0
            };
            return await contactService_1.contactService.create(supaData);
        }
        else {
            const contact = new Contact_1.Contact(contactData);
            return await contact.save();
        }
    }
    async updateContact(id, updates) {
        if (this.useSupabase) {
            // Mapear campos se necessário
            const supaUpdates = {};
            if (updates.name)
                supaUpdates.name = updates.name;
            if (updates.phone)
                supaUpdates.phone = updates.phone;
            if (updates.babyGender)
                supaUpdates.baby_gender = updates.babyGender;
            if (updates.isActive !== undefined)
                supaUpdates.is_active = updates.isActive;
            if (updates.lastInteraction)
                supaUpdates.last_interaction = updates.lastInteraction;
            if (updates.registrationStatus)
                supaUpdates.registration_status = updates.registrationStatus;
            if (updates.evaluationStartDate)
                supaUpdates.evaluation_start_date = updates.evaluationStartDate;
            if (updates.evaluationMessages !== undefined)
                supaUpdates.evaluation_messages = updates.evaluationMessages;
            if (updates.interestScore !== undefined)
                supaUpdates.interest_score = updates.interestScore;
            return await contactService_1.contactService.update(id, supaUpdates);
        }
        else {
            return await Contact_1.Contact.findByIdAndUpdate(id, updates, { new: true }).lean();
        }
    }
    async countContacts(filters = {}) {
        if (this.useSupabase) {
            return await contactService_1.contactService.count({
                isActive: filters.isActive
            });
        }
        else {
            return await Contact_1.Contact.countDocuments(filters);
        }
    }
    async countContactsByBabyGender() {
        if (this.useSupabase) {
            return await contactService_1.contactService.countByBabyGender();
        }
        else {
            const [male, female, unknown] = await Promise.all([
                Contact_1.Contact.countDocuments({ isActive: true, babyGender: 'male' }),
                Contact_1.Contact.countDocuments({ isActive: true, babyGender: 'female' }),
                Contact_1.Contact.countDocuments({ isActive: true, babyGender: 'unknown' })
            ]);
            return { male, female, unknown };
        }
    }
    async getContactRegistrationsByMonth() {
        if (this.useSupabase) {
            return await contactService_1.contactService.getRegistrationsByMonth();
        }
        else {
            // Implementação MongoDB existente
            const sixMonthsAgo = new Date();
            sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
            const registrations = await Contact_1.Contact.aggregate([
                {
                    $match: {
                        isActive: true,
                        createdAt: { $gte: sixMonthsAgo }
                    }
                },
                {
                    $group: {
                        _id: {
                            year: { $year: '$createdAt' },
                            month: { $month: '$createdAt' }
                        },
                        count: { $sum: 1 }
                    }
                },
                {
                    $sort: { '_id.year': 1, '_id.month': 1 }
                }
            ]);
            const monthNames = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            return registrations.map(item => ({
                name: `${monthNames[item._id.month - 1]}/${item._id.year}`,
                value: item.count
            }));
        }
    }
    // =====================================================
    // MÉTODOS PARA MESSAGES
    // =====================================================
    async findAllMessages(filters = {}) {
        if (this.useSupabase) {
            return await messageService_1.messageService.findAll({
                contactId: filters.contact,
                fromMe: filters.fromMe,
                limit: filters.limit,
                offset: filters.skip
            });
        }
        else {
            let query = Message_1.Message.find();
            if (filters.contact) {
                query = query.where('contact', filters.contact);
            }
            if (filters.fromMe !== undefined) {
                query = query.where('fromMe', filters.fromMe);
            }
            if (filters.limit) {
                query = query.limit(filters.limit);
            }
            if (filters.skip) {
                query = query.skip(filters.skip);
            }
            return await query.sort({ timestamp: -1 }).lean();
        }
    }
    async createMessage(messageData) {
        if (this.useSupabase) {
            // Mapear campos do MongoDB para Supabase
            const supaData = {
                contact_id: messageData.contact,
                content: messageData.content,
                timestamp: messageData.timestamp || new Date().toISOString(),
                type: messageData.type || 'text',
                from_me: messageData.fromMe,
                message_id: messageData.messageId,
                status: messageData.status,
                whatsapp_data: messageData.whatsappData,
                media_data: messageData.mediaData,
                sentiment: messageData.sentiment,
                category: messageData.category
            };
            return await messageService_1.messageService.create(supaData);
        }
        else {
            const message = new Message_1.Message(messageData);
            return await message.save();
        }
    }
    async countMessages(filters = {}) {
        if (this.useSupabase) {
            return await messageService_1.messageService.count({
                contactId: filters.contact,
                fromMe: filters.fromMe
            });
        }
        else {
            return await Message_1.Message.countDocuments(filters);
        }
    }
    async countMessagesToday() {
        if (this.useSupabase) {
            return await messageService_1.messageService.countToday();
        }
        else {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            return await Message_1.Message.countDocuments({
                timestamp: { $gte: today }
            });
        }
    }
    // =====================================================
    // MÉTODO PARA ALTERNAR BANCO
    // =====================================================
    switchToSupabase() {
        this.useSupabase = true;
        console.log('🔄 Alternado para Supabase');
    }
    switchToMongoDB() {
        this.useSupabase = false;
        console.log('🔄 Alternado para MongoDB');
    }
    isUsingSupabase() {
        return this.useSupabase;
    }
}
exports.DatabaseAdapter = DatabaseAdapter;
// Instância singleton
exports.dbAdapter = new DatabaseAdapter();
exports.default = exports.dbAdapter;
