import React from 'react';
import { WhatsAppMonitor } from '../components/whatsapp/WhatsAppMonitor';

export const WhatsAppPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-2">
            <span className="text-3xl">📱</span>
            <h1 className="text-3xl font-bold text-gray-900">
              WhatsApp Automático
            </h1>
          </div>
          <p className="text-lg text-gray-600">
            Monitoramento e controle do sistema de processamento automático de áudios via WhatsApp
          </p>
        </div>

        {/* Informações do Sistema */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <span className="text-2xl">🤖</span>
            </div>
            <div>
              <h3 className="text-lg font-medium text-blue-900 mb-2">
                Sistema de Processamento Automático
              </h3>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• <strong>Recebimento automático</strong> de mensagens de áudio via WhatsApp</p>
                <p>• <strong>Processamento em background</strong> com fila inteligente e priorização</p>
                <p>• <strong>Respostas automáticas</strong> personalizadas pela IA Rafaela</p>
                <p>• <strong>Criação automática</strong> de contatos para números desconhecidos</p>
                <p>• <strong>Monitoramento em tempo real</strong> com estatísticas detalhadas</p>
              </div>
            </div>
          </div>
        </div>

        {/* Fluxo do Sistema */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            🔄 Fluxo de Processamento
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🎵</span>
              </div>
              <h4 className="font-medium text-gray-900">Áudio Recebido</h4>
              <p className="text-xs text-gray-500 mt-1">Via WhatsApp</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">✅</span>
              </div>
              <h4 className="font-medium text-gray-900">Confirmação</h4>
              <p className="text-xs text-gray-500 mt-1">Imediata</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">📋</span>
              </div>
              <h4 className="font-medium text-gray-900">Fila</h4>
              <p className="text-xs text-gray-500 mt-1">Background</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">🤖</span>
              </div>
              <h4 className="font-medium text-gray-900">IA Gemini</h4>
              <p className="text-xs text-gray-500 mt-1">Processamento</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-xl">💬</span>
              </div>
              <h4 className="font-medium text-gray-900">Resposta</h4>
              <p className="text-xs text-gray-500 mt-1">Automática</p>
            </div>
          </div>
          
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-600">
              ⏱️ Tempo total: ~15-20 segundos | 🎯 Taxa de sucesso: >95%
            </p>
          </div>
        </div>

        {/* Monitor Principal */}
        <WhatsAppMonitor />

        {/* Instruções */}
        <div className="bg-gray-50 rounded-lg p-6 mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            📋 Como Usar
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">🚀 Inicialização</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Clique em "Iniciar" para ativar o serviço</li>
                <li>Escaneie o QR Code com WhatsApp</li>
                <li>Aguarde conexão e autenticação</li>
                <li>Sistema ficará ativo automaticamente</li>
              </ol>
            </div>
            
            <div>
              <h4 className="font-medium text-gray-900 mb-2">🧪 Testes</h4>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>Digite um número no campo de teste</li>
                <li>Clique em "Teste" para enviar mensagem</li>
                <li>Use "Testar Áudio" para simular processamento</li>
                <li>Monitore estatísticas em tempo real</li>
              </ol>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-start space-x-2">
              <span className="text-yellow-600 text-lg">⚠️</span>
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Importante:</p>
                <ul className="space-y-1 list-disc list-inside">
                  <li>Mantenha o WhatsApp conectado no dispositivo principal</li>
                  <li>O sistema funciona 24/7 após configuração inicial</li>
                  <li>Reconexão automática em caso de desconexão</li>
                  <li>Logs detalhados disponíveis para debugging</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Recursos Avançados */}
        <div className="bg-white rounded-lg shadow p-6 mt-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            🔧 Recursos Avançados
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🎯</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Priorização Inteligente</h4>
              <p className="text-sm text-gray-600">
                Contatos conhecidos têm prioridade alta. Sistema aprende padrões de uso.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🔄</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Retry Automático</h4>
              <p className="text-sm text-gray-600">
                Falhas são automaticamente reprocessadas com backoff exponencial.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">📊</span>
              </div>
              <h4 className="font-medium text-gray-900 mb-2">Analytics Detalhado</h4>
              <p className="text-sm text-gray-600">
                Métricas completas de performance, tempo de resposta e taxa de sucesso.
              </p>
            </div>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="bg-gray-900 text-white rounded-lg p-6 mt-8">
          <h3 className="text-lg font-medium mb-4">
            🔌 API Endpoints
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm font-mono">
            <div>
              <p className="text-green-400">GET /api/whatsapp-auto/status</p>
              <p className="text-gray-400 mb-2">Status completo do sistema</p>
              
              <p className="text-blue-400">POST /api/whatsapp-auto/start</p>
              <p className="text-gray-400 mb-2">Iniciar serviço</p>
              
              <p className="text-red-400">POST /api/whatsapp-auto/stop</p>
              <p className="text-gray-400 mb-2">Parar serviço</p>
            </div>
            
            <div>
              <p className="text-yellow-400">POST /api/whatsapp-auto/restart</p>
              <p className="text-gray-400 mb-2">Reiniciar serviço</p>
              
              <p className="text-purple-400">GET /api/whatsapp-auto/health</p>
              <p className="text-gray-400 mb-2">Health check</p>
              
              <p className="text-orange-400">POST /api/whatsapp-auto/send-test</p>
              <p className="text-gray-400 mb-2">Enviar mensagem de teste</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
