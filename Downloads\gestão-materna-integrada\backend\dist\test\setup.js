"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongodb_memory_server_1 = require("mongodb-memory-server");
const mongoose_1 = __importDefault(require("mongoose"));
const dotenv_1 = __importDefault(require("dotenv"));
// Carregar variáveis de ambiente de teste
dotenv_1.default.config({ path: '.env.test' });
let mongoServer;
// Configuração global antes de todos os testes
beforeAll(async () => {
    // Criar servidor MongoDB em memória
    mongoServer = await mongodb_memory_server_1.MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    // Conectar ao MongoDB de teste
    await mongoose_1.default.connect(mongoUri);
    console.log('🧪 MongoDB de teste conectado');
});
// Limpeza após cada teste
afterEach(async () => {
    // Limpar todas as coleções
    const collections = mongoose_1.default.connection.collections;
    for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
    }
});
// Limpeza após todos os testes
afterAll(async () => {
    // Fechar conexão com MongoDB
    await mongoose_1.default.connection.dropDatabase();
    await mongoose_1.default.connection.close();
    // Parar servidor MongoDB em memória
    await mongoServer.stop();
    console.log('🧪 MongoDB de teste desconectado');
});
// Configurações globais para testes
jest.setTimeout(30000);
// Mock de console para reduzir ruído nos testes
global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
};
