2025/06/11-12:54:15.178 4abc Reusing MANIFEST C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\tokens\rafaela-audio\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/MANIFEST-000001
2025/06/11-12:54:15.179 4abc Recovering log #800
2025/06/11-12:54:15.184 4abc Reusing old log C:\Users\<USER>\Downloads\gestão-materna-integrada\backend\tokens\rafaela-audio\Default\IndexedDB\https_web.whatsapp.com_0.indexeddb.leveldb/000800.log 
2025/06/11-12:54:15.184 4abc Delete type=2 #799
2025/06/11-12:54:15.202 79bc Level-0 table #805: started
2025/06/11-12:54:15.213 79bc Level-0 table #805: 102975 bytes OK
2025/06/11-12:54:15.213 79bc Delete type=0 #800
2025/06/11-12:54:15.214 4abc Manual compaction at level-0 from ' \x08\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x09\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0a\x01\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 490366 : 1
2025/06/11-12:54:15.225 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.252 4abc Generated table #806@0: 61410 keys, 530514 bytes
2025/06/11-12:54:15.252 4abc Compacted 1@0 + 1@1 files => 530514 bytes
2025/06/11-12:54:15.252 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.252 4abc Delete type=2 #805
2025/06/11-12:54:15.253 4abc Manual compaction at level-0 from ' \x0a\x01\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 490366 : 1 .. ' \x09\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:15.260 4abc Level-0 table #808: started
2025/06/11-12:54:15.261 4abc Level-0 table #808: 6305 bytes OK
2025/06/11-12:54:15.262 4abc Delete type=2 #802
2025/06/11-12:54:15.262 4abc Delete type=0 #804
2025/06/11-12:54:15.263 4abc Manual compaction at level-0 from ' \x06\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x07\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0b\x01\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 493234 : 1
2025/06/11-12:54:15.263 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.289 4abc Generated table #809@0: 61467 keys, 531984 bytes
2025/06/11-12:54:15.289 4abc Compacted 1@0 + 1@1 files => 531984 bytes
2025/06/11-12:54:15.289 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.289 4abc Delete type=2 #808
2025/06/11-12:54:15.290 4abc Manual compaction at level-0 from ' \x0b\x01\x00\x00\xc8\x11\x00w\x00o\x00r\x00k\x00e\x00r\x00_\x00w\x00a\x00m\x00_\x00e\x00v\x00e\x00n\x00t\x00s' @ 493234 : 1 .. ' \x07\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:15.291 4abc Level-0 table #811: started
2025/06/11-12:54:15.292 4abc Level-0 table #811: 416 bytes OK
2025/06/11-12:54:15.293 4abc Delete type=2 #806
2025/06/11-12:54:15.293 4abc Delete type=0 #807
2025/06/11-12:54:15.294 4abc Manual compaction at level-0 from ' \x09\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0a\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x09\x01\x00\x00\xc8\x06\x00s\x00t\x00a\x00t\x00u\x00s' @ 493353 : 0
2025/06/11-12:54:15.294 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.322 4abc Generated table #812@0: 61451 keys, 531583 bytes
2025/06/11-12:54:15.322 4abc Compacted 1@0 + 1@1 files => 531583 bytes
2025/06/11-12:54:15.322 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.322 4abc Delete type=2 #811
2025/06/11-12:54:15.323 79bc Manual compaction at level-0 from ' \x09\x01\x00\x00\xc8\x06\x00s\x00t\x00a\x00t\x00u\x00s' @ 493353 : 0 .. ' \x0a\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:15.324 4abc Level-0 table #814: started
2025/06/11-12:54:15.325 4abc Level-0 table #814: 14900 bytes OK
2025/06/11-12:54:15.325 4abc Delete type=2 #809
2025/06/11-12:54:15.325 4abc Delete type=0 #810
2025/06/11-12:54:15.326 4abc Manual compaction at level-0 from '\x00\xf9\x00\x00\x00' @ 72057594037927935 : 1 .. '\x00\xfa\x00\x00\x00' @ 0 : 0; will stop at '\x00\xf9\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 494207 : 0
2025/06/11-12:54:15.326 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.347 4abc Generated table #815@0: 60601 keys, 498110 bytes
2025/06/11-12:54:15.348 4abc Compacted 1@0 + 1@1 files => 498110 bytes
2025/06/11-12:54:15.348 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.348 4abc Delete type=2 #814
2025/06/11-12:54:15.349 4abc Manual compaction at level-0 from '\x00\xf9\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 494207 : 0 .. '\x00\xfa\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:15.350 4abc Level-0 table #817: started
2025/06/11-12:54:15.351 4abc Level-0 table #817: 3862 bytes OK
2025/06/11-12:54:15.351 4abc Delete type=2 #812
2025/06/11-12:54:15.351 4abc Delete type=0 #813
2025/06/11-12:54:15.353 4abc Manual compaction at level-0 from ' \x05\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x06\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0b\x01\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 494255 : 1
2025/06/11-12:54:15.353 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.375 4abc Generated table #818@0: 60612 keys, 500225 bytes
2025/06/11-12:54:15.375 4abc Compacted 1@0 + 1@1 files => 500225 bytes
2025/06/11-12:54:15.376 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.376 4abc Delete type=2 #817
2025/06/11-12:54:15.376 4abc Manual compaction at level-0 from ' \x0b\x01\x06 \x03\x00\x00\x00\x00\x00\x80V@\x00\x01\x0b\x00I\x00d\x00T\x00t\x00l\x009\x000\x00D\x00a\x00y\x00s' @ 494255 : 1 .. ' \x06\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:15.377 4abc Level-0 table #820: started
2025/06/11-12:54:15.378 4abc Level-0 table #820: 503 bytes OK
2025/06/11-12:54:15.378 4abc Delete type=2 #815
2025/06/11-12:54:15.378 4abc Delete type=0 #816
2025/06/11-12:54:15.379 4abc Manual compaction at level-0 from ' \x0a\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0b\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0a\x01\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 494353 : 0
2025/06/11-12:54:15.379 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:15.395 4abc Generated table #821@0: 60591 keys, 499761 bytes
2025/06/11-12:54:15.395 4abc Compacted 1@0 + 1@1 files => 499761 bytes
2025/06/11-12:54:15.395 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:15.396 4abc Delete type=2 #820
2025/06/11-12:54:15.396 4abc Manual compaction at level-0 from ' \x0a\x01\x00\x00\xc8\x0d\x00l\x00o\x00c\x00a\x00l\x00_\x00s\x00t\x00o\x00r\x00a\x00g\x00e' @ 494353 : 0 .. ' \x0b\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.564 37e8 Level-0 table #823: started
2025/06/11-12:54:16.565 37e8 Level-0 table #823: 19274 bytes OK
2025/06/11-12:54:16.566 37e8 Delete type=2 #818
2025/06/11-12:54:16.566 37e8 Delete type=0 #819
2025/06/11-12:54:16.566 37e8 Manual compaction at level-0 from ' \x0c\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0d\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x16\x01\x00\x00\x05' @ 495308 : 1
2025/06/11-12:54:16.566 37e8 Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.585 37e8 Generated table #824@0: 60852 keys, 507876 bytes
2025/06/11-12:54:16.585 37e8 Compacted 1@0 + 1@1 files => 507876 bytes
2025/06/11-12:54:16.585 37e8 compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.586 37e8 Delete type=2 #823
2025/06/11-12:54:16.586 4abc Manual compaction at level-0 from ' \x16\x01\x00\x00\x05' @ 495308 : 1 .. ' \x0d\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.587 4abc Level-0 table #826: started
2025/06/11-12:54:16.588 4abc Level-0 table #826: 4149 bytes OK
2025/06/11-12:54:16.588 4abc Delete type=2 #821
2025/06/11-12:54:16.588 4abc Delete type=0 #822
2025/06/11-12:54:16.589 4abc Manual compaction at level-0 from ' \x0d\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0e\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x19\x01\x00\x00\x05' @ 495644 : 1
2025/06/11-12:54:16.589 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.607 4abc Generated table #827@0: 60951 keys, 510534 bytes
2025/06/11-12:54:16.608 4abc Compacted 1@0 + 1@1 files => 510534 bytes
2025/06/11-12:54:16.608 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.608 4abc Delete type=2 #826
2025/06/11-12:54:16.609 4abc Manual compaction at level-0 from ' \x19\x01\x00\x00\x05' @ 495644 : 1 .. ' \x0e\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.609 4abc Level-0 table #829: started
2025/06/11-12:54:16.610 4abc Level-0 table #829: 252 bytes OK
2025/06/11-12:54:16.610 4abc Delete type=2 #824
2025/06/11-12:54:16.610 4abc Delete type=0 #825
2025/06/11-12:54:16.611 4abc Manual compaction at level-0 from ' \x0e\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x0f\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0e\x01\x00\x00\x05' @ 495664 : 0
2025/06/11-12:54:16.611 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.628 4abc Generated table #830@0: 60949 keys, 510350 bytes
2025/06/11-12:54:16.628 4abc Compacted 1@0 + 1@1 files => 510350 bytes
2025/06/11-12:54:16.628 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.628 4abc Delete type=2 #829
2025/06/11-12:54:16.629 4abc Manual compaction at level-0 from ' \x0e\x01\x00\x00\x05' @ 495664 : 0 .. ' \x0f\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.629 4abc Level-0 table #832: started
2025/06/11-12:54:16.631 4abc Level-0 table #832: 252 bytes OK
2025/06/11-12:54:16.631 4abc Delete type=2 #827
2025/06/11-12:54:16.631 4abc Delete type=0 #828
2025/06/11-12:54:16.632 4abc Manual compaction at level-0 from ' \x0f\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x10\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x0f\x01\x00\x00\x05' @ 495670 : 0
2025/06/11-12:54:16.632 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.653 4abc Generated table #833@0: 60947 keys, 510279 bytes
2025/06/11-12:54:16.653 4abc Compacted 1@0 + 1@1 files => 510279 bytes
2025/06/11-12:54:16.653 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.653 4abc Delete type=2 #832
2025/06/11-12:54:16.653 4abc Manual compaction at level-0 from ' \x0f\x01\x00\x00\x05' @ 495670 : 0 .. ' \x10\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.654 4abc Level-0 table #835: started
2025/06/11-12:54:16.655 4abc Level-0 table #835: 253 bytes OK
2025/06/11-12:54:16.656 4abc Delete type=2 #830
2025/06/11-12:54:16.656 4abc Delete type=0 #831
2025/06/11-12:54:16.657 4abc Manual compaction at level-0 from ' \x10\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x11\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x10\x01\x00\x00\x05' @ 495676 : 0
2025/06/11-12:54:16.657 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.673 4abc Generated table #836@0: 60945 keys, 510238 bytes
2025/06/11-12:54:16.673 4abc Compacted 1@0 + 1@1 files => 510238 bytes
2025/06/11-12:54:16.673 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.673 4abc Delete type=2 #835
2025/06/11-12:54:16.674 4abc Manual compaction at level-0 from ' \x10\x01\x00\x00\x05' @ 495676 : 0 .. ' \x11\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.674 4abc Level-0 table #838: started
2025/06/11-12:54:16.675 4abc Level-0 table #838: 252 bytes OK
2025/06/11-12:54:16.676 4abc Delete type=2 #833
2025/06/11-12:54:16.676 4abc Delete type=0 #834
2025/06/11-12:54:16.676 4abc Manual compaction at level-0 from ' \x11\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x12\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x11\x01\x00\x00\x05' @ 495682 : 0
2025/06/11-12:54:16.676 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.695 4abc Generated table #839@0: 60943 keys, 510429 bytes
2025/06/11-12:54:16.695 4abc Compacted 1@0 + 1@1 files => 510429 bytes
2025/06/11-12:54:16.695 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.695 4abc Delete type=2 #838
2025/06/11-12:54:16.696 4abc Manual compaction at level-0 from ' \x11\x01\x00\x00\x05' @ 495682 : 0 .. ' \x12\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.696 4abc Level-0 table #841: started
2025/06/11-12:54:16.697 4abc Level-0 table #841: 252 bytes OK
2025/06/11-12:54:16.698 4abc Delete type=2 #836
2025/06/11-12:54:16.698 4abc Delete type=0 #837
2025/06/11-12:54:16.699 4abc Manual compaction at level-0 from ' \x12\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x13\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x12\x01\x00\x00\x05' @ 495688 : 0
2025/06/11-12:54:16.699 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.718 4abc Generated table #842@0: 60941 keys, 510395 bytes
2025/06/11-12:54:16.719 4abc Compacted 1@0 + 1@1 files => 510395 bytes
2025/06/11-12:54:16.719 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.719 4abc Delete type=2 #841
2025/06/11-12:54:16.719 4abc Manual compaction at level-0 from ' \x12\x01\x00\x00\x05' @ 495688 : 0 .. ' \x13\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.720 4abc Level-0 table #844: started
2025/06/11-12:54:16.721 4abc Level-0 table #844: 252 bytes OK
2025/06/11-12:54:16.721 4abc Delete type=2 #839
2025/06/11-12:54:16.721 4abc Delete type=0 #840
2025/06/11-12:54:16.722 4abc Manual compaction at level-0 from ' \x13\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x14\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x13\x01\x00\x00\x05' @ 495694 : 0
2025/06/11-12:54:16.722 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.738 4abc Generated table #845@0: 60939 keys, 510396 bytes
2025/06/11-12:54:16.738 4abc Compacted 1@0 + 1@1 files => 510396 bytes
2025/06/11-12:54:16.739 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.739 4abc Delete type=2 #844
2025/06/11-12:54:16.739 4abc Manual compaction at level-0 from ' \x13\x01\x00\x00\x05' @ 495694 : 0 .. ' \x14\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.740 4abc Level-0 table #847: started
2025/06/11-12:54:16.741 4abc Level-0 table #847: 252 bytes OK
2025/06/11-12:54:16.742 4abc Delete type=2 #842
2025/06/11-12:54:16.742 4abc Delete type=0 #843
2025/06/11-12:54:16.742 4abc Manual compaction at level-0 from ' \x14\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x15\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x14\x01\x00\x00\x05' @ 495700 : 0
2025/06/11-12:54:16.742 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.758 4abc Generated table #848@0: 60937 keys, 510316 bytes
2025/06/11-12:54:16.758 4abc Compacted 1@0 + 1@1 files => 510316 bytes
2025/06/11-12:54:16.758 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.759 4abc Delete type=2 #847
2025/06/11-12:54:16.759 4abc Manual compaction at level-0 from ' \x14\x01\x00\x00\x05' @ 495700 : 0 .. ' \x15\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.760 4abc Level-0 table #850: started
2025/06/11-12:54:16.761 4abc Level-0 table #850: 252 bytes OK
2025/06/11-12:54:16.764 4abc Delete type=2 #845
2025/06/11-12:54:16.764 4abc Delete type=0 #846
2025/06/11-12:54:16.765 4abc Manual compaction at level-0 from ' \x15\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x16\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x15\x01\x00\x00\x05' @ 495706 : 0
2025/06/11-12:54:16.765 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.795 4abc Generated table #851@0: 60935 keys, 510330 bytes
2025/06/11-12:54:16.795 4abc Compacted 1@0 + 1@1 files => 510330 bytes
2025/06/11-12:54:16.795 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.795 4abc Delete type=2 #850
2025/06/11-12:54:16.796 4abc Manual compaction at level-0 from ' \x15\x01\x00\x00\x05' @ 495706 : 0 .. ' \x16\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.797 4abc Level-0 table #853: started
2025/06/11-12:54:16.798 4abc Level-0 table #853: 252 bytes OK
2025/06/11-12:54:16.798 4abc Delete type=2 #848
2025/06/11-12:54:16.798 4abc Delete type=0 #849
2025/06/11-12:54:16.799 4abc Manual compaction at level-0 from ' \x16\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x16\x01\x00\x00\x05' @ 495712 : 0
2025/06/11-12:54:16.799 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.825 4abc Generated table #854@0: 60933 keys, 510057 bytes
2025/06/11-12:54:16.825 4abc Compacted 1@0 + 1@1 files => 510057 bytes
2025/06/11-12:54:16.826 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.826 4abc Delete type=2 #853
2025/06/11-12:54:16.826 4abc Manual compaction at level-0 from ' \x16\x01\x00\x00\x05' @ 495712 : 0 .. ' \x17\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:54:16.828 4abc Level-0 table #856: started
2025/06/11-12:54:16.830 4abc Level-0 table #856: 1193 bytes OK
2025/06/11-12:54:16.830 4abc Delete type=2 #851
2025/06/11-12:54:16.830 4abc Delete type=0 #852
2025/06/11-12:54:16.831 4abc Manual compaction at level-0 from ' \x19\x01\x00\x00\x00' @ 72057594037927935 : 1 .. ' \x1a\x01\x00\x00\x00' @ 0 : 0; will stop at ' \x19\x01\x00\x00\x05' @ 495832 : 0
2025/06/11-12:54:16.831 4abc Compacting 1@0 + 1@1 files
2025/06/11-12:54:16.857 4abc Generated table #857@0: 60931 keys, 508601 bytes
2025/06/11-12:54:16.858 4abc Compacted 1@0 + 1@1 files => 508601 bytes
2025/06/11-12:54:16.858 4abc compacted to: files[ 0 1 1 0 0 0 0 ]
2025/06/11-12:54:16.858 4abc Delete type=2 #856
2025/06/11-12:54:16.859 4abc Manual compaction at level-0 from ' \x19\x01\x00\x00\x05' @ 495832 : 0 .. ' \x1a\x01\x00\x00\x00' @ 0 : 0; will stop at (end)
2025/06/11-12:56:59.781 4abc Compacting 1@1 + 1@2 files
2025/06/11-12:56:59.788 4abc Generated table #858@1: 470 keys, 14755 bytes
2025/06/11-12:56:59.788 4abc Compacted 1@1 + 1@2 files => 14755 bytes
2025/06/11-12:56:59.788 4abc compacted to: files[ 0 0 1 0 0 0 0 ]
2025/06/11-12:56:59.789 4abc Delete type=2 #103
2025/06/11-12:56:59.789 4abc Delete type=2 #854
2025/06/11-12:56:59.789 4abc Delete type=2 #857
2025/06/11-12:57:00.313 37e8 Level-0 table #860: started
2025/06/11-12:57:00.331 37e8 Level-0 table #860: 1041652 bytes OK
2025/06/11-12:57:00.332 37e8 Delete type=0 #855
2025/06/11-12:57:00.333 37e8 Compacting 1@1 + 1@2 files
2025/06/11-12:57:00.346 37e8 Generated table #861@1: 13583 keys, 473066 bytes
2025/06/11-12:57:00.346 37e8 Compacted 1@1 + 1@2 files => 473066 bytes
2025/06/11-12:57:00.346 37e8 compacted to: files[ 0 0 1 0 0 0 0 ]
