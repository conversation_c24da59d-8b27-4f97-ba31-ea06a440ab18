[0613/052010.408:INFO:CONSOLE(0)] "[object PromiseRejectionEvent]", source:  (0)
[0613/052010.409:INFO:CONSOLE(150)] "Uncaught (in promise) [object Object]", source: https://static.whatsapp.net/rsrc.php/v4/y7/r/MJ9ScsSSMFm.js (150)
[0613/052142.196:WARNING:spdy_session.cc(3234)] Received RST for invalid stream11
[0613/052146.501:INFO:CONSOLE(0)] "[object PromiseRejectionEvent]", source:  (0)
[0613/052146.501:INFO:CONSOLE(150)] "Uncaught (in promise) [object Object]", source: https://static.whatsapp.net/rsrc.php/v4/y7/r/MJ9ScsSSMFm.js (150)
[0613/052209.731:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052209.731:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052209.732:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052209.732:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052209.732:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052209.732:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052209.827:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052209.827:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052217.309:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052222.314:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052225.167:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052225.167:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052226.608:INFO:CONSOLE(0)] "[object PromiseRejectionEvent]", source:  (0)
[0613/052226.609:INFO:CONSOLE(72)] "Uncaught (in promise) Invariant Violation: Minified invariant #56367; %s", source: https://static.whatsapp.net/rsrc.php/v4/y7/r/MJ9ScsSSMFm.js (72)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0613/052228.436:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0613/052228.438:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/?post_logout=1&logout_reason=0 (0)
[0613/052228.682:WARNING:spdy_session.cc(3502)] Received HEADERS for invalid stream 103
[0613/052239.696:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052239.697:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052239.700:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052239.700:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052239.700:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052239.700:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052239.864:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052239.864:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052247.545:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052251.453:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052254.233:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052254.233:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052255.697:INFO:CONSOLE(0)] "[object PromiseRejectionEvent]", source:  (0)
[0613/052255.697:INFO:CONSOLE(72)] "Uncaught (in promise) Invariant Violation: Minified invariant #56367; %s", source: https://static.whatsapp.net/rsrc.php/v4/y7/r/MJ9ScsSSMFm.js (72)
[0613/052257.384:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0613/052257.385:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/?post_logout=1&logout_reason=0 (0)
[0613/052257.620:WARNING:spdy_session.cc(3234)] Received RST for invalid stream113
[0613/052309.777:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052309.777:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052309.777:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052309.778:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052309.778:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052309.778:ERROR:gpu_disk_cache.cc(673)] Gpu Cache Creation failed: -2
[0613/052309.877:ERROR:cache_util_win.cc(20)] Unable to move the cache: Acesso negado. (0x5)
[0613/052309.877:ERROR:disk_cache.cc(205)] Unable to create cache
[0613/052317.437:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052321.322:ERROR:quota_database.cc(929)] Could not open the quota database, resetting.
[0613/052324.098:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052324.098:ERROR:quota_database.cc(931)] Failed to reset the quota database.
[0613/052325.566:INFO:CONSOLE(0)] "[object PromiseRejectionEvent]", source:  (0)
[0613/052325.566:INFO:CONSOLE(72)] "Uncaught (in promise) Invariant Violation: Minified invariant #56367; %s", source: https://static.whatsapp.net/rsrc.php/v4/y7/r/MJ9ScsSSMFm.js (72)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Feature xr-spatial-tracking's parameters are ignored.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'attribution-reporting'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'compute-pressure'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'interest-cohort'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-issuance'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Origin trial controlled feature not enabled: 'shared-storage'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'shared-storage-select-url'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'private-state-token-redemption'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'unload'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Error with Permissions-Policy header: Unrecognized feature: 'window-management'.", source:  (0)
[0613/052327.328:INFO:CONSOLE(0)] "Document-Policy HTTP header: Unrecognized document policy feature name include-js-call-stacks-in-crash-reports.", source: https://web.whatsapp.com/?post_logout=1&logout_reason=0 (0)
[0613/052327.570:WARNING:spdy_session.cc(3234)] Received RST for invalid stream115
[0613/052327.571:WARNING:spdy_session.cc(3234)] Received RST for invalid stream113
[0613/052327.571:WARNING:spdy_session.cc(3234)] Received RST for invalid stream105
[0613/052327.571:WARNING:spdy_session.cc(3234)] Received RST for invalid stream117
[0613/052327.571:WARNING:spdy_session.cc(3234)] Received RST for invalid stream109
[0613/052327.571:WARNING:spdy_session.cc(3234)] Received RST for invalid stream101
