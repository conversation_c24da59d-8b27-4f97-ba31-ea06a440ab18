"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mongoose_1 = __importDefault(require("mongoose"));
const Contact_1 = require("../models/Contact");
const database_1 = require("../config/database");
/**
 * Script para resetar o status de um contato específico
 * Útil para testes e desenvolvimento
 */
async function resetContactStatus() {
    try {
        console.log('🔄 Conectando ao banco de dados...');
        await (0, database_1.connectDatabase)();
        // Telefone específico que queremos resetar
        const phoneToReset = '(84) 98850-1582';
        console.log(`🔍 Procurando contato com telefone: ${phoneToReset}`);
        const contact = await Contact_1.Contact.findOne({ phone: phoneToReset });
        if (!contact) {
            console.log('❌ Contato não encontrado');
            return;
        }
        console.log('📋 Contato encontrado:');
        console.log(`   Nome: ${contact.name}`);
        console.log(`   Telefone: ${contact.phone}`);
        console.log(`   Status atual: ${contact.registrationStatus}`);
        console.log(`   Ativo: ${contact.isActive}`);
        // Resetar para status inicial
        contact.registrationStatus = 'unregistered';
        contact.isActive = true;
        contact.evaluationStartDate = new Date();
        contact.evaluationMessages = 0;
        contact.interestScore = 0;
        contact.lastInteraction = new Date();
        await contact.save();
        console.log('✅ Status do contato resetado com sucesso!');
        console.log('📋 Novo status:');
        console.log(`   Status: ${contact.registrationStatus}`);
        console.log(`   Ativo: ${contact.isActive}`);
        console.log(`   Data de avaliação: ${contact.evaluationStartDate}`);
    }
    catch (error) {
        console.error('❌ Erro ao resetar status do contato:', error);
    }
    finally {
        await mongoose_1.default.connection.close();
        console.log('🔌 Conexão com banco fechada');
    }
}
// Função para resetar todos os contatos não interessados
async function resetAllNotInterestedContacts() {
    try {
        console.log('🔄 Conectando ao banco de dados...');
        await (0, database_1.connectDatabase)();
        console.log('🔍 Procurando contatos marcados como "not_interested"...');
        const contacts = await Contact_1.Contact.find({ registrationStatus: 'not_interested' });
        console.log(`📋 Encontrados ${contacts.length} contatos não interessados`);
        if (contacts.length === 0) {
            console.log('✅ Nenhum contato para resetar');
            return;
        }
        // Resetar todos
        const result = await Contact_1.Contact.updateMany({ registrationStatus: 'not_interested' }, {
            $set: {
                registrationStatus: 'unregistered',
                isActive: true,
                evaluationStartDate: new Date(),
                evaluationMessages: 0,
                interestScore: 0,
                lastInteraction: new Date()
            }
        });
        console.log(`✅ ${result.modifiedCount} contatos resetados com sucesso!`);
    }
    catch (error) {
        console.error('❌ Erro ao resetar contatos:', error);
    }
    finally {
        await mongoose_1.default.connection.close();
        console.log('🔌 Conexão com banco fechada');
    }
}
// Verificar argumentos da linha de comando
const args = process.argv.slice(2);
if (args.includes('--all')) {
    console.log('🔄 Resetando TODOS os contatos não interessados...');
    resetAllNotInterestedContacts();
}
else {
    console.log('🔄 Resetando contato específico...');
    console.log('💡 Use --all para resetar todos os contatos não interessados');
    resetContactStatus();
}
