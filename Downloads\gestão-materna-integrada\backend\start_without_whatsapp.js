// Script para iniciar o servidor sem WhatsApp (para testes)
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

console.log('🚀 Iniciando servidor SEM WhatsApp (modo teste)...');

const app = express();

// Middlewares básicos
app.use(helmet());
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 100 // máximo 100 requests por IP
});
app.use(limiter);

// Socket.IO
const httpServer = createServer(app);
const io = new Server(httpServer, {
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    methods: ['GET', 'POST']
  }
});

// Inicializar apenas o Gemini (sem WhatsApp)
let geminiService;

async function initializeServices() {
  try {
    console.log('🤖 Inicializando Gemini AI...');
    const { GeminiAIService } = require('./dist/services/gemini');
    geminiService = new GeminiAIService();

    // O Gemini já inicializa automaticamente no construtor
    console.log('✅ Gemini AI inicializado com sucesso!');
  } catch (error) {
    console.error('❌ Erro ao inicializar Gemini:', error);
    console.log('⚠️ Continuando sem Gemini...');
    geminiService = null;
  }
}

// Configurar rotas básicas
function setupRoutes() {
  // Rota de teste
  app.get('/api/test', (req, res) => {
    res.json({ 
      status: 'ok', 
      message: 'Servidor funcionando sem WhatsApp',
      timestamp: new Date().toISOString()
    });
  });

  // Rota para gerar follow-up (PRINCIPAL)
  app.post('/api/ai/generate-followup', async (req, res) => {
    try {
      const { contactId } = req.body;

      if (!contactId) {
        return res.status(400).json({ error: 'ID do contato é obrigatório' });
      }

      console.log('🤖 Gerando acompanhamento para contato:', contactId);

      // Buscar dados do contato no Supabase
      const { contactService } = require('./dist/services/contactService');
      const contact = await contactService.findById(contactId);

      if (!contact) {
        return res.status(404).json({ error: 'Contato não encontrado' });
      }

      console.log('👤 Contato encontrado:', {
        name: contact.name,
        phone: contact.phone,
        babyGender: contact.baby_gender
      });

      // Converter para IContact
      const iContact = {
        name: contact.name,
        phone: contact.phone,
        babyGender: contact.baby_gender || 'unknown',
        isActive: contact.is_active,
        lastInteraction: new Date(contact.last_interaction),
        registrationStatus: contact.registration_status,
        evaluationMessages: contact.evaluation_messages,
        interestScore: contact.interest_score,
        evaluationStartDate: contact.evaluation_start_date ? new Date(contact.evaluation_start_date) : undefined,
        createdAt: new Date(contact.created_at || ''),
        updatedAt: new Date(contact.updated_at || ''),
        updateInteraction: async () => {},
        incrementEvaluationMessages: async () => {},
        updateInterestScore: async () => {},
        markAsRegistered: async () => {},
        markAsNotInterested: async () => {},
        deactivate: async () => {},
        reactivate: async () => {}
      };

      console.log('🤖 Gerando mensagem com IA...');

      if (!geminiService) {
        throw new Error('Gemini AI não está disponível');
      }

      const followUpMessage = await geminiService.generateFollowUpMessage(iContact);
      console.log('✅ Mensagem gerada:', followUpMessage.substring(0, 100) + '...');

      res.json({
        message: followUpMessage,
        contactId: contactId,
        contactName: contact.name,
        timestamp: new Date().toISOString(),
        status: 'success'
      });
    } catch (error) {
      console.error('❌ Erro ao gerar acompanhamento:', error);
      res.status(500).json({ 
        error: error.message,
        details: 'Erro interno do servidor ao gerar mensagem de acompanhamento'
      });
    }
  });

  // Função para simular validação de número (modo teste)
  function simulateNumberValidation(phone) {
    const cleanPhone = phone.replace(/\D/g, '');
    // Simula diferentes formatos para teste
    const formats = [
      `55${cleanPhone}@c.us`,
      `${cleanPhone}@c.us`
    ];
    return formats[0]; // Retorna o primeiro formato para simulação
  }

  // Rota para enviar mensagem via WhatsApp (simulada)
  app.post('/api/whatsapp-auto/send-message', async (req, res) => {
    try {
      const { phoneNumber, message } = req.body;

      if (!phoneNumber || !message) {
        return res.status(400).json({
          success: false,
          error: 'Número de telefone e mensagem são obrigatórios'
        });
      }

      console.log(`📤 Simulando envio de mensagem para ${phoneNumber}...`);

      // Simular validação do telefone
      const whatsappPhone = simulateNumberValidation(phoneNumber);
      console.log(`📞 Telefone simulado: ${phoneNumber} → ${whatsappPhone}`);

      // Simular envio (sem WhatsApp real)
      console.log(`💬 Mensagem: ${message.substring(0, 100)}...`);
      console.log('✅ Mensagem "enviada" com sucesso (modo simulação)');

      res.json({
        success: true,
        message: `Mensagem enviada para ${phoneNumber}`,
        data: {
          to: phoneNumber,
          whatsappFormat: whatsappPhone,
          content: message
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error(`❌ Erro ao enviar mensagem:`, error);
      res.status(500).json({
        success: false,
        error: 'Falha ao enviar mensagem'
      });
    }
  });

  // Importar outras rotas (sem WhatsApp)
  try {
    const { setupRoutes: setupMainRoutes } = require('./dist/routes/index');
    setupMainRoutes(app, null, geminiService); // null = sem WhatsApp
    console.log('✅ Rotas principais configuradas');
  } catch (error) {
    console.warn('⚠️ Erro ao configurar rotas principais:', error.message);
  }

  console.log('✅ Rotas configuradas (modo sem WhatsApp)');
}

// Inicializar servidor
async function startServer() {
  try {
    await initializeServices();
    setupRoutes();

    const PORT = process.env.PORT || 3334;
    
    httpServer.listen(PORT, () => {
      console.log(`\n🎉 SERVIDOR INICIADO COM SUCESSO!`);
      console.log(`📡 Porta: ${PORT}`);
      console.log(`🤖 Gemini AI: ✅ Ativo`);
      console.log(`📱 WhatsApp: ❌ Desabilitado (modo teste)`);
      console.log(`🔗 URL: http://localhost:${PORT}`);
      console.log(`\n💡 Para testar o botão de follow-up:`);
      console.log(`   1. Acesse o dashboard`);
      console.log(`   2. Clique no coração 💝 de uma gestante`);
      console.log(`   3. A mensagem será gerada pela IA`);
      console.log(`\n🚀 Sistema pronto para uso!`);
    });

  } catch (error) {
    console.error('❌ Erro fatal ao iniciar servidor:', error);
    process.exit(1);
  }
}

// Tratamento de sinais
process.on('SIGINT', () => {
  console.log('\n🛑 Recebido SIGINT, encerrando servidor...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Recebido SIGTERM, encerrando servidor...');
  process.exit(0);
});

// Iniciar
startServer();
