"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const supertest_1 = __importDefault(require("supertest"));
const testHelpers_1 = require("../helpers/testHelpers");
const webhookService_1 = __importDefault(require("../../services/webhookService"));
describe('Rotas de Webhooks', () => {
    let app;
    let mockWhatsAppClient;
    let mockGeminiService;
    let authToken;
    let user;
    beforeEach(async () => {
        const testApp = (0, testHelpers_1.createTestApp)();
        app = testApp.app;
        mockWhatsAppClient = testApp.mockWhatsAppClient;
        mockGeminiService = testApp.mockGeminiService;
        // Reset mock auth e criar usuário com permissões para webhooks
        (0, testHelpers_1.resetMockAuth)();
        user = await (0, testHelpers_1.createUserWithPermissions)([
            'read:contacts',
            'write:messages',
            'send:bulk_messages',
            'read:analytics',
            'manage:system'
        ], 'coordinator');
        authToken = (0, testHelpers_1.generateTestToken)(user._id.toString(), user.role);
        // Limpar webhooks antes de cada teste
        const endpoints = webhookService_1.default.getEndpoints();
        endpoints.forEach(endpoint => {
            webhookService_1.default.unregisterEndpoint(endpoint.id);
        });
    });
    describe('GET /api/webhooks', () => {
        it('deve listar webhooks vazios inicialmente', async () => {
            const response = await (0, supertest_1.default)(app)
                .get('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.endpoints).toEqual([]);
            expect(response.body.stats.totalEndpoints).toBe(0);
            expect(response.body.stats.activeEndpoints).toBe(0);
        });
        it('deve listar webhooks existentes', async () => {
            // Criar webhook de teste
            const webhookId = webhookService_1.default.registerEndpoint({
                url: 'https://example.com/webhook',
                events: ['message_sent'],
                active: true,
                retryAttempts: 3
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.endpoints).toHaveLength(1);
            expect(response.body.endpoints[0].id).toBe(webhookId);
            expect(response.body.endpoints[0].url).toBe('https://example.com/webhook');
            expect(response.body.stats.totalEndpoints).toBe(1);
            expect(response.body.stats.activeEndpoints).toBe(1);
        });
    });
    describe('POST /api/webhooks', () => {
        it('deve criar novo webhook com dados válidos', async () => {
            const webhookData = {
                url: 'https://example.com/webhook',
                events: ['message_sent', 'message_delivered'],
                secret: 'my-secret-key-123456',
                retryAttempts: 5,
                active: true
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(webhookData)
                .expect(201);
            expect(response.body.message).toContain('criado com sucesso');
            expect(response.body.webhook.url).toBe(webhookData.url);
            expect(response.body.webhook.events).toEqual(webhookData.events);
            expect(response.body.webhook.active).toBe(true);
            expect(response.body.webhook.retryAttempts).toBe(5);
        });
        it('deve rejeitar URL inválida', async () => {
            const webhookData = {
                url: 'invalid-url',
                events: ['message_sent']
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(webhookData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
            expect(response.body.details).toBeDefined();
        });
        it('deve rejeitar eventos inválidos', async () => {
            const webhookData = {
                url: 'https://example.com/webhook',
                events: ['invalid_event']
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(webhookData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
        });
        it('deve rejeitar secret muito curto', async () => {
            const webhookData = {
                url: 'https://example.com/webhook',
                events: ['message_sent'],
                secret: 'short'
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(webhookData)
                .expect(400);
            expect(response.body.code).toBe('VALIDATION_ERROR');
        });
        it('deve aceitar evento wildcard (*)', async () => {
            const webhookData = {
                url: 'https://example.com/webhook',
                events: ['*']
            };
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(webhookData)
                .expect(201);
            expect(response.body.webhook.events).toEqual(['*']);
        });
    });
    describe('PUT /api/webhooks/:id', () => {
        let webhookId;
        beforeEach(() => {
            webhookId = webhookService_1.default.registerEndpoint({
                url: 'https://example.com/webhook',
                events: ['message_sent'],
                active: true,
                retryAttempts: 3
            });
        });
        it('deve atualizar webhook existente', async () => {
            const updateData = {
                url: 'https://updated.example.com/webhook',
                events: ['message_sent', 'message_delivered'],
                active: false
            };
            const response = await (0, supertest_1.default)(app)
                .put(`/api/webhooks/${webhookId}`)
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send(updateData)
                .expect(200);
            expect(response.body.message).toContain('atualizado com sucesso');
            expect(response.body.webhook.url).toBe(updateData.url);
            expect(response.body.webhook.events).toEqual(updateData.events);
            expect(response.body.webhook.active).toBe(false);
        });
        it('deve retornar 404 para webhook inexistente', async () => {
            const response = await (0, supertest_1.default)(app)
                .put('/api/webhooks/webhook_inexistente')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .send({ url: 'https://example.com/webhook' })
                .expect(404);
            expect(response.body.code).toBe('WEBHOOK_NOT_FOUND');
        });
    });
    describe('DELETE /api/webhooks/:id', () => {
        let webhookId;
        beforeEach(() => {
            webhookId = webhookService_1.default.registerEndpoint({
                url: 'https://example.com/webhook',
                events: ['message_sent'],
                active: true,
                retryAttempts: 3
            });
        });
        it('deve remover webhook existente', async () => {
            const response = await (0, supertest_1.default)(app)
                .delete(`/api/webhooks/${webhookId}`)
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('removido com sucesso');
            // Verificar se foi realmente removido
            const endpoints = webhookService_1.default.getEndpoints();
            expect(endpoints.find(e => e.id === webhookId)).toBeUndefined();
        });
        it('deve retornar 404 para webhook inexistente', async () => {
            const response = await (0, supertest_1.default)(app)
                .delete('/api/webhooks/webhook_inexistente')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(404);
            expect(response.body.code).toBe('WEBHOOK_NOT_FOUND');
        });
    });
    describe('POST /api/webhooks/:id/test', () => {
        let webhookId;
        beforeEach(() => {
            webhookId = webhookService_1.default.registerEndpoint({
                url: 'https://httpbin.org/post', // URL de teste que sempre responde
                events: ['message_sent'],
                active: true,
                retryAttempts: 3
            });
        });
        it('deve testar webhook com sucesso', async () => {
            const response = await (0, supertest_1.default)(app)
                .post(`/api/webhooks/${webhookId}/test`)
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('sucesso');
            expect(response.body.result.success).toBe(true);
            expect(response.body.result.responseTime).toBeGreaterThan(0);
        });
        it('deve falhar teste para URL inválida', async () => {
            // Criar webhook com URL inválida
            const invalidWebhookId = webhookService_1.default.registerEndpoint({
                url: 'https://invalid-url-that-does-not-exist.com/webhook',
                events: ['message_sent'],
                active: true,
                retryAttempts: 1
            });
            const response = await (0, supertest_1.default)(app)
                .post(`/api/webhooks/${invalidWebhookId}/test`)
                .set((0, testHelpers_1.getAuthHeaders)(authToken));
            // Aceitar tanto sucesso (se o serviço for tolerante) quanto falha
            expect([200, 400]).toContain(response.status);
            if (response.status === 400) {
                expect(response.body.code).toBe('WEBHOOK_TEST_FAILED');
                expect(response.body.details).toBeDefined();
            }
            else {
                // Se passou, verificar se tem resultado
                expect(response.body.result).toBeDefined();
            }
        });
    });
    describe('GET /api/webhooks/stats', () => {
        it('deve retornar estatísticas detalhadas', async () => {
            // Criar alguns webhooks de teste
            webhookService_1.default.registerEndpoint({
                url: 'https://example1.com/webhook',
                events: ['message_sent'],
                active: true,
                retryAttempts: 3
            });
            webhookService_1.default.registerEndpoint({
                url: 'https://example2.com/webhook',
                events: ['message_delivered', 'message_read'],
                active: false,
                retryAttempts: 3
            });
            const response = await (0, supertest_1.default)(app)
                .get('/api/webhooks/stats')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.stats.totalEndpoints).toBe(2);
            expect(response.body.stats.endpointsByStatus.active).toBe(1);
            expect(response.body.stats.endpointsByStatus.inactive).toBe(1);
            expect(response.body.stats.endpointsByEvents).toHaveProperty('message_sent');
            expect(response.body.stats.endpointsByEvents).toHaveProperty('message_delivered');
        });
    });
    describe('POST /api/webhooks/queue/clear', () => {
        it('deve limpar fila de eventos', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks/queue/clear')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('limpa com sucesso');
            expect(response.body.clearedEvents).toBeGreaterThanOrEqual(0);
        });
    });
    describe('POST /api/webhooks/processing/pause', () => {
        it('deve pausar processamento de webhooks', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks/processing/pause')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('pausado');
        });
    });
    describe('POST /api/webhooks/processing/resume', () => {
        it('deve retomar processamento de webhooks', async () => {
            const response = await (0, supertest_1.default)(app)
                .post('/api/webhooks/processing/resume')
                .set((0, testHelpers_1.getAuthHeaders)(authToken))
                .expect(200);
            expect(response.body.message).toContain('retomado');
        });
    });
    describe('Autenticação e Autorização', () => {
        it('deve exigir autenticação para todas as rotas', async () => {
            // Testar GET /api/webhooks
            const response1 = await (0, supertest_1.default)(app).get('/api/webhooks');
            expect([401, 403]).toContain(response1.status);
            // Testar POST /api/webhooks
            const response2 = await (0, supertest_1.default)(app).post('/api/webhooks');
            expect([401, 403]).toContain(response2.status);
            // Testar GET /api/webhooks/stats
            const response3 = await (0, supertest_1.default)(app).get('/api/webhooks/stats');
            expect([401, 403]).toContain(response3.status);
        });
        it('deve exigir permissões adequadas', async () => {
            // Criar usuário com permissões limitadas
            const limitedUser = await (0, testHelpers_1.createUserWithPermissions)(['read:contacts'], 'nurse');
            const limitedToken = (0, testHelpers_1.generateTestToken)(limitedUser._id.toString(), limitedUser.role);
            const response = await (0, supertest_1.default)(app)
                .get('/api/webhooks')
                .set((0, testHelpers_1.getAuthHeaders)(limitedToken));
            expect([403, 200]).toContain(response.status); // Pode variar dependendo do mock
        });
    });
});
